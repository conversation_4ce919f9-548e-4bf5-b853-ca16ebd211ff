using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using ExcelToData;
using GameServer;

namespace ExcelToData
{
    class ExportClass
    { /// <summary>
      /// 自动生成读写配置的类
      /// </summary>
      /// <param name="excelType"></param>
      /// <param name="fileName"></param>
        public static void WriteClass(Dictionary<string, string> excelType, string fileName)
        {
            //设置生成的C#类目录，我这里是直接放到了项目下 
            string CurDir = Environment.CurrentDirectory.Replace("\\", "/") + "/Assets/Data/Scripts/Configure/";
            //判断文件夹是否存在,不存在就创建这个文件夹
            if (!Directory.Exists(CurDir))
            {
                Directory.CreateDirectory(CurDir);
            }

            //生成的是c#类   所以需要加上.cs后缀  这里的Pathname为生成类的名字
            string FilePath = CurDir + fileName + ".cs";
            //文件覆盖方式添加内容
            StreamWriter file = new StreamWriter(FilePath, false);
            //保存数据到文件(将C#类需要的头文件先写入)
            file.Write("using System.Collections;\n");
            file.Write("using System.Collections.Generic;\n");
            //file.Write("using UnityEngine;\n");
            file.Write("using System.IO;\n");
            file.Write("\n");
            //写入类名(与文件名相同)
            file.Write("public class ");
            file.Write(fileName);

            file.Write("\n{\n");

            file.Write("public static float versition = 0;\n\n");


            foreach (var item in excelType)
            {

                // [parName] = "type, description"
                // 判断数组
                string typeLabel = item.Value.Split(',')[0];
                // Replace *s to *[]
                String convertedType = isArray(typeLabel)
                    // if last character equal 's'
                    ? tsCoverType(typeLabel.Remove(typeLabel.Length - 1)) + "[]"
                    : tsCoverType(typeLabel);


                file.Write($"\tpublic {convertedType} {item.Key};// {item.Value.Split(',')[1]}\n");

            }
            file.Write("\n\n");
            ///写入数据存储数组
            file.Write("public static Dictionary<uint," + fileName + "> lists = new Dictionary<uint," + fileName + ">();");

            /////////////////////////////////////////////////////////////////
            //克隆
            file.Write("\t\n");
            file.Write("public static " + fileName + " clone(" + fileName + " old)\n{\n");
            file.Write(fileName + " clone = new " + fileName + "();\n");
            foreach (var item in excelType)
            {
                file.Write("\tclone." + item.Key + "=" + "old." + item.Key + ";\n");
                file.Write("\t\n");
            }
            file.Write("return clone;\n}\n");
            ////////////////////////////////////////////////////////////////
            file.Write("\t\n");
            //写入自动读取方法
            file.Write("public void parse(BinaryReader br){ ");

            ///文件头信息
            file.Write(" int length = br.ReadInt32();\n\n");

            file.Write("for (int i = 0; i < length; i++)\n{");
            file.Write(" Console.WriteLine(br.ReadString()  + br.ReadString());\n}\n");



            file.Write("int row = br.ReadInt32();\n");
            file.Write("int length2 = br.ReadInt32();\n");

            // file.Write("Console.WriteLine(length);\n");
            //file.Write("Console.WriteLine(length2);\n");


            file.Write(" for (int i = 0; i < row; i++)\n{ ");
            file.Write("\n\n");
            file.Write(fileName + " baseData = new " + fileName + " ();");

            file.Write("\t\n");
            foreach (var item in excelType)
            {

                string typeName = item.Value.Split(',')[0];
                if (isArray(typeName))
                {
                    typeName = typeName.Remove(typeName.Length - 1);
                    string readArray = $"((Func<{typeName}[]>)(() => {{" +
                        $"var len = br.{ExcelAccess.typeToReadFunctionName("uint")}();" +
                        $"var cache = new {typeName}[len];" +
                        $"for(var i=0;i<len;i++) {{" +
                            $"cache.push(br.{ExcelAccess.typeToReadFunctionName(typeName)}());" +
                        $"}}" +
                        $"return cache;" +
                    $"}}))()";
                    file.Write($"\tbaseData.{item.Key} = {readArray};\n");
                }
                else
                {
                    file.Write($"\tbaseData.{item.Key} = br.{ExcelAccess.typeToReadFunctionName(typeName)}();\n");
                }


                // file.Write("\tbaseData. " + item.Key + "=" + "br." + ExcelAccess.typeToReadFunctionName(item.Value.Split(',')[0]) + "();\n");
                file.Write("\t\n");
            }


            file.Write("lists[baseData.id] = baseData;");

            file.Write("\t\n}");
            file.Write("\t\n}");



            // 属性列表和基础运算方法
            file.Write("\tprivate static string[] properties = {");
            foreach (var item in excelType)
            {
                file.Write($"\"{item.Key}\",");
            }
            file.Write("};\n" +
                // a、添加通用方法：直接提供同数据的所有属性相加，相减，限制相加（提供多一个装备作为限制装备传入）然后相加属性不能大于限制装备的属性没如果大于则等于最大装备属性
                $"\tpublic static {fileName} add({fileName} a, {fileName} b, uint start = 0, uint end, {fileName} limit = null) {{\n" +
                $"\t\tif(!a || !b) return null;\n" +
                $"\t\t{fileName} result = this.clone(a);\n" +
                $"\t\tfor(var i = Math.Max(start, 0), e = Math.Min(end, this.properties.length); i < e; i++) {{\n" +
                $"\t\t\tvar par = this.properties[i];\n" +
                $"\t\t\tresult[par] += b[par];\n" +
                $"\t\t\tif(limit && result[par] > limit[par])\n" +
                $"\t\t\t\tresult[par] = limit[par];\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                $"\tpublic static {fileName} sub({fileName} a, {fileName} b, uint start = 0, uint end) {{\n" +
                $"\t\tif(!a || !b) return null;\n" +
                $"\t\tlet result = this.clone(a);\n" +
                $"\t\tfor(let i = Math.Max(start, 0), e = Math.Min(end, this.properties.length); i < e; i++) {{\n" +
                $"\t\t\tvar par = this.properties[i];\n" +
                $"\t\t\tresult[par] -= b[par];\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // c、添加一个随机方法，然后传入一件随机属性用装备，一次检查属性不为0的，然后随机一个值出来。然后生成一个json返回。如果传入一件原始装备则，返回json并且把属性添加到原始装备上去 random(item,item=null)
                $"\tpublic static string random({fileName} src, uint i = 0) {{\n" +
                $"\t\tif(src[this.properties[i]] == 0) // NOTE:\n" +
                $"\t\t\tsrc[this.properties[i]] = new Random().Next();\n" +
                $"\t\treturn JSON.stringify(src);\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // d、添加一个比较方法如果a的数值大于装备b，那就返回true
                $"\tpublic static bool large({fileName} a, {fileName} b, uint i = 0) {{\n" +
                $"\t\treturn a[this.properties[i]] > b[this.properties[i]];\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // e、添加一个max的方法，然后调用d方法，然后返回处返回最大的属性的那个装备
                $"\tpublic static {fileName} max({fileName} a, {fileName} b, uint i = 0) {{\n" +
                $"\t\tif(a[this.properties[i]] > b[this.properties[i]])\n" +
                $"\t\t\treturn a;\n" +
                $"\t\treturn b;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // f、添加一个json方法，传入json然后把属性和相应的值添加到属性中，（json里面就纯粹的属性名字和属性）
                $"\tpublic static json({fileName} a, dynamic data) {{\n" +
                $"\t\tdata = JSON.parse(data);\n" +
                $"\t\tfor(let k in data) {{\n" +
                $"\t\t\ta[k] = data[k];\n" +
                $"\t\t}};\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // i、添加一个方法，然后把单个属性更新到数值当中去。
                $"\tpublic static {fileName} setProperty({fileName} a, uint p, dynamic value) {{\n" +
                $"\t\ta[this.properties[p]] = value;\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n");



            file.Write("\n}");
            //关闭文件
            file.Close();
            //释放对象
            file.Dispose();

        }

        public static bool isArray(string tex)
        {
            return tex[tex.Length - 1] == 's' || tex.IndexOf("[]") != -1;
        }

        /// <summary>
        /// 判断类型是否为值类型
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <returns>是否为值类型</returns>
        /// <summary>
        /// 判断类型是否为值类型
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <returns>是否为值类型</returns>
        public static bool IsValueType(string typeName)
        {
            // 数组类型和字符串是引用类型，不应该为可空
            if (typeName.Contains("[]") ) return true;

            switch (typeName.ToLower())
            {
                case "int":
                case "uint":
                case "long":
                case "ulong":
                case "short":
                case "ushort":
                case "byte":
                case "sbyte":
                case "float":
                case "double":
                case "decimal":
                case "char":
                case "date":
                case "Vector2":
                case "Vector3":
                case "Vector4":
                case "string":
                    return true;
                default:
                    return false;
            }
        }
        public static void WriteTypescriptClass(Dictionary<string, string> excelType, string fileName, string CurDir = "")
        {
            //设置生成的C#类目录，我这里是直接放到了项目下 
            if (CurDir == "")
            {
                CurDir = AppConfig.Instance.tsOutPatch;
            }
            else
            {
                // CurDir += "/TSConfigure/";
                CurDir += "/script/tsScript/";
            }

            //判断文件夹是否存在,不存在就创建这个文件夹
            if (!Directory.Exists(CurDir))
            {
                Directory.CreateDirectory(CurDir);
            }

            //生成的是c#类   所以需要加上.cs后缀  这里的Pathname为生成类的名字
            string FilePath = CurDir + fileName + ".ts";
            //文件覆盖方式添加内容
            StreamWriter file = new StreamWriter(FilePath, false);
            //保存数据到文件(将ts#类需要的头文件先写入)

            file.Write("//请在项目中，将 SyncObject、cMap 注入到 gd3d.__ExcDate__ 对象上。\n ");

            // file.Write("import { Dictionary } from \"./Dictionary\";\n");
            //file.Write("import { cMap } from \"../Data/Map\";\n");
            //file.Write("import { ioTool } from \"../Tool/ioTool\";\n");
            //file.Write("import { SyncObject } from \"../../Src/Net/RPC/RPCManager\";\n");
            string ns = AppConfig.Instance.defNamespace;
            if (!string.IsNullOrEmpty(ns))
                file.Write($"namespace {ns}{{");

            file.Write("declare let gd3d;\n");

            file.Write("@gd3d.__ExcDate__.SyncObject\n");
            file.Write("export class " + fileName + "{\n");

            file.Write("\tpublic static versition:number = 0;\n\n");

            file.Write("\t/** \n");
            file.Write("\t* 从服务器同步数据到本地\n");
            file.Write("\t* @param fields 指定需要同步的字段 例如[\"name\",\"desc\"]\n");
            file.Write("\t*/\n");
            file.Write("\tpublic sync:(fields?:string[]) => Promise<void>;\n\n");

            file.Write("\t/** \n");
            file.Write("\t* 保存数据到服务器\n");
            file.Write("\t* @param fields 指定需要保存的字段 例如[\"name\",\"desc\"]\n");
            file.Write("\t*/\n");
            file.Write("\tpublic save:(fields?:string[]) => Promise<void>;\n\n");

            file.Write("\t/** \n");
            file.Write("\t* 获取数据数量\n");
            file.Write("\t*/\n");
            file.Write("\tpublic static getlistCount:() => Promise<number>;\n\n");

            file.Write("\t/** \n");
            file.Write("\t* 获取列表数据\n");
            file.Write("\t* @param offset 从什么位置获取 默认值:0\n");
            file.Write("\t* @param count 指定需要保存的字段 例如[\"name\",\"desc\"]\n");
            file.Write("\t*/\n");
            file.Write($"\tpublic static getlist:(offset?:number, count?:number) => Promise<{fileName}>;\n\n");

            foreach (var item in excelType)
            {
                // [parName] = "type, description"
                // 判断数组
                string typeLabel = item.Value.Split(',')[0];
                // Replace *s to *[]
                String convertedType = isArray(typeLabel)
                    // if last character equal 's'
                    ? tsCoverType(typeLabel.Remove(typeLabel.Length - 1)) + "[]"
                    : tsCoverType(typeLabel);

                file.Write("\t/**" + item.Value.Split(',')[1] + "*/\n\tpublic " + item.Key + ":" + convertedType + ";\n");
            }
            file.Write("\n\n");
            ///写入数据存储数组
            // file.Write("public static list: Dictionary=new Dictionary() ;");

            //file.Write("public static list: cMap<" + fileName + ">=new cMap() ;");
            file.Write("private static _list ;");
            file.Write("static get list(){ if(!this._list ){this._list = new gd3d.__ExcDate__.cMap()}; return this._list;};");

            /////////////////////////////////////////////////////////////////
            //克隆
            file.Write("\t\n");



            file.Write("public static " + " parseData(br):void {\n");
            //file.Write("var data :" + fileName + " = new " + fileName + "();\n");

            ///文件头信息
            file.Write(" var length:number = br.readInt32();\n\n");

            file.Write("for (var i = 0; i < length; i++)\n{");

            string b = "var b:string = br." + ExcelAccess.typeTSToReadFunctionName("string") + "();\n";
            file.Write(b);
            file.Write("var bb:string = br." + ExcelAccess.typeTSToReadFunctionName("string") + "();\n");


            file.Write("\t\n}\t\n");


            file.Write("var row:number = br.readInt32();\n");
            file.Write("var length2:number = br.readInt32();\n");




            file.Write(" for (var i = 0; i < row; i++)\n{ ");
            file.Write("\n\n");
            file.Write("var baseData:" + fileName + " = new " + fileName + " ();");

            file.Write("\t\n");
            bool firstread = true;
            string firstkey = "";
            foreach (var item in excelType)
            {
                if (firstread)
                {
                    firstkey = item.Key;
                    firstread = false;
                }
                string typeName = item.Value.Split(',')[0];
                if (isArray(typeName))
                {
                    typeName = typeName.Remove(typeName.Length - 1);
                    string readArray = "(() => { let cache = []; let len = br." + ExcelAccess.typeTSToReadFunctionName("uint") + "(); for(let i=0;i<len;i++) {cache.push(br." + ExcelAccess.typeTSToReadFunctionName(typeName) + "());}return cache;})()";
                    file.Write("\tbaseData." + item.Key + " = " + readArray + ";\n");
                }
                else
                {
                    file.Write("\tbaseData." + item.Key + "=" + "br." + ExcelAccess.typeTSToReadFunctionName(item.Value.Split(',')[0]) + "();\n");
                }
                file.Write("\t\n");
            }
            //////////////////
            file.Write("\tthis.list.set(baseData." + firstkey + ", baseData);\n");
            //////////////////

            //file.Write("this.list.Add(baseData.id, baseData);");

            //file.Write("\t\n}\n");
            //file.Write(" for (let i = 0; i < this.list.count; i++)\n{\n ");
            //file.Write("let fbd:" + fileName + " = this.list.values[i];\n");
            //file.Write("let guanka:GuanKaConfigData= new GuanKaConfigData();\n");
            //file.Write("guanka.rolesDic = new Dictionary();\n");
            //file.Write("let mainRoleData: roleBaseData = new roleBaseData();\n");
            //file.Write("mainRoleData.paoLen = fbd.paoLen;\n");
            //file.Write("mainRoleData.paoJiaPos = fbd.paoJiaPos;\n");
            //file.Write("mainRoleData.camToTrans = fbd.camToTrans;\n");
            //file.Write("mainRoleData.paoInitAngle = fbd.paoInitAngle;\n");
            //file.Write("mainRoleData.camMoveTrans = fbd.camMoveTrans;\n");
            //file.Write("mainRoleData.paoAngleXMax = fbd.paoAngleXMax;\n");
            //file.Write("mainRoleData.paoAngleXMin = fbd.paoAngleXMin;\n");
            //file.Write("mainRoleData.endModelName = fbd.endModelName;\n");
            //file.Write("mainRoleData.endPointObjArr = fbd.endPointObjArr;\n");
            //file.Write("guanka.rolesDic.Add(TargetType.left, mainRoleData);\n");

            //file.Write("guanka.index=fbd.id;\n");
            //file.Write("guanka.aiMatch = fbd.aiMatch == 1;\n");
            //file.Write("guanka.sceneName=fbd.sceneName;\n");
            //file.Write("guanka.speedNum=fbd.speedNum;\n");
            //file.Write("guanka.gravity=fbd.gravity;\n");
            //file.Write("guanka.accelerateNum=fbd.accelerateNum;\n");
            //file.Write("guanka.windMinSpeed=fbd.windMinSpeed;\n");
            //file.Write("guanka.windMaxSpeed=fbd.windMaxSpeed;\n");
            //file.Write("guanka.windnum=fbd.windnum;\n");
            //file.Write("guanka.timeLimit=fbd.timeLimit;\n");
            //file.Write("guanka.initPaoNum=fbd.initPaoNum;\n");
            //file.Write("guanka.targetObjList = new Array<TargetObjData>();\n");

            //file.Write(" for (let k = 1; k < fbd.tagersum+1; k++)\n{\n");
            //file.Write("let targetObjList:TargetObjData = new TargetObjData();\n");
            //file.Write("targetObjList.modelName= fbd['modelName'+k];\n");
            //file.Write("targetObjList.tarType= fbd['tarType'+k];\n");
            //file.Write("targetObjList.moveType= fbd['moveType'+k];\n");
            //file.Write("targetObjList.hp= fbd['hp'+k];\n");
            //file.Write("targetObjList.outdamage= fbd['damge1'+k];\n");
            //file.Write("targetObjList.rotation= fbd['rotation'+k];\n");
            //file.Write("targetObjList.pos= fbd['pos'+k];\n");
            //file.Write("targetObjList.moveSpeed= fbd['moveSpeed'+k];\n");
            //file.Write("targetObjList.moveLength= fbd['moveLength'+k];\n");
            //file.Write("targetObjList.pointArr= fbd['pointArr'+k];\n");
            //file.Write("guanka.targetObjList.push(targetObjList);\n");
            //file.Write("\n}\n");
            //file.Write("if (fbd.reshs && fbd.reshs.length != 0) {\n");
            //file.Write("if (!guanka.reshs) {\t\n");
            //file.Write("guanka.reshs = new Array<reshBoduan>();\n");
            //file.Write("}\t\n");
            //file.Write("for (let k = 0, len = fbd.reshs.length; k < len; k++) {\n");
            //file.Write("var boduan = new reshBoduan();\n");
            //file.Write("var boduanArr = fbd.reshs[k];\n");
            //file.Write("boduan.rshTime = boduanArr[0];\n");
            //file.Write("boduan.reshArr = boduanArr.slice(1);\n");
            //file.Write("guanka.reshs.push(boduan);\n");
            //file.Write("}}\n");
            //file.Write("\nGuanKaConfigManager.Instance.list.Add(guanka.index, guanka);");
            //file.Write("\t\n}");
            //file.Write("\n console.error('end')");
            //file.Write("\t\n}\n");







            file.Write("\n}\n}\npublic static " + " clone(old:" + fileName + "):" + fileName + "{\n");
            file.Write("var clone :" + fileName + " = new " + fileName + "();\n");
            foreach (var item in excelType)
            {
                file.Write("\tclone." + item.Key + "=" + "old." + item.Key + ";\n");
                file.Write("\t\n");
            }
            file.Write("return clone;\n}\n");


            // 属性列表和基础运算方法
            file.Write($"\tprivate static params = [");
            foreach (var item in excelType)
            {
                file.Write($"\"{item.Key}\",");
            }
            file.Write($"];\n" +
                // a、添加通用方法：直接提供同数据的所有属性相加，相减，限制相加（提供多一个装备作为限制装备传入）然后相加属性不能大于限制装备的属性没如果大于则等于最大装备属性
                $"\tpublic static add(a: {fileName}, b: {fileName}, start: number = 0, end: number, limit: {fileName} = null) {{\n" +
                $"\t\tif(!a || !b) return null;\n" +
                $"\t\tlet result = this.clone(a);\n" +
                $"\t\tfor(let i = Math.max(start, 0), e = Math.min(end, this.params.length); i < e; i++) {{\n" +
                $"\t\t\tconst par = this.params[i];\n" +
                $"\t\t\tresult[par] += b[par];\n" +
                $"\t\t\tif(limit && result[par] > limit[par])\n" +
                $"\t\t\t\tresult[par] = limit[par];\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                $"\tpublic static sub(a: {fileName}, b: {fileName}, start: number = 0, end: number) {{\n" +
                $"\t\tif(!a || !b) return null;\n" +
                $"\t\tlet result = this.clone(a);\n" +
                $"\t\tfor(let i = Math.max(start, 0), e = Math.min(end, this.params.length); i < e; i++) {{\n" +
                $"\t\t\tconst par = this.params[i];\n" +
                $"\t\t\tresult[par] -= b[par];\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // c、添加一个随机方法，然后传入一件随机属性用装备，一次检查属性不为0的，然后随机一个值出来。然后生成一个json返回。如果传入一件原始装备则，返回json并且把属性添加到原始装备上去 random(item,item=null)
                $"\tpublic static random(src: {fileName}, i: number = 0) {{\n" +
                $"\t\tif(src[this.params[i]] == 0) // NOTE:\n" +
                $"\t\t\tsrc[this.params[i]] = Math.random();\n" +
                $"\t\treturn JSON.stringify(src);\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // d、添加一个比较方法如果a的数值大于装备b，那就返回true
                $"\tpublic static large(a: {fileName}, b: {fileName}, i: number = 0) {{\n" +
                $"\t\treturn a[this.params[i]] > b[this.params[i]];\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // e、添加一个max的方法，然后调用d方法，然后返回处返回最大的属性的那个装备
                $"\tpublic static max(a: {fileName}, b: {fileName}, i: number = 0) {{\n" +
                $"\t\tif(a[this.params[i]] > b[this.params[i]])\n" +
                $"\t\t\treturn a;\n" +
                $"\t\treturn b;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // f、添加一个json方法，传入json然后把属性和相应的值添加到属性中，（json里面就纯粹的属性名字和属性）
                $"\tpublic static json(a: {fileName}, data) {{\n" +
                $"\t\tdata = JSON.parse(data);\n" +
                $"\t\tfor(let k in data) {{\n" +
                $"\t\t\ta[k] = data[k];\n" +
                $"\t\t}};\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n");

            file.Write($"\n" +
                // i、添加一个方法，然后把单个属性更新到数值当中去。
                $"\tpublic static setProperty(a: {fileName}, p: number, value) {{\n" +
                $"\t\ta[this.params[p]] = value;\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n");

            file.Write("\n}");

            //加入列表，便于项目统一初始化
            file.Write($"if(!gd3d.__ExcDate__)gd3d.__ExcDate__= {{ }} ; if(!gd3d.__ExcDate__.__list) gd3d.__ExcDate__.__list = []; gd3d.__ExcDate__.__list.push({fileName});");
            if (!string.IsNullOrEmpty(ns))
                file.Write("}");

            //关闭文件
            file.Close();
            //释放对象
            file.Dispose();
        }

        public static string genORM(Dictionary<string, string> excelType, string fileName, string excelName)
        {
            // TODO:
            string header = "using System;\n"
                + "using Microsoft.EntityFrameworkCore;\n"
                + "using System.ComponentModel.DataAnnotations;\n"
                + "using System.Data;\n"
                + "using Newtonsoft.Json;\n"
                + "using Newtonsoft.Json.Serialization;\n"
                + "using System.Collections.Generic;\n"
                + "using Newtonsoft.Json.Linq;\n";

            // 第一个属性通常是ID 作为主键
            string props = "\t[Key]\n";
            // 额外的主键
            props += "\tpublic uint dbID // ORM Table ID\n"
                + "\t{\n"
                + "\t\tget;set;\n"
                + "\t}\n";

            // ToString method
            string toStr = "return \"\"";

            // Properties
            string propList = "";


            string setValues = "";

            // 属性类型字典 JSON需要
            string tyList = "";


            int offset = 0;
            // Properties
            foreach (var item in excelType)
            {
                string typeName = item.Value.Split(',')[0];

                if (typeName == "date")
                    typeName = "long";

                if (item.Key == "")
                    continue;
                // TODO: handle array
                if (isArray(typeName))
                    typeName = "string";
                if (typeName == "vector3" || typeName == "vector4")
                    typeName = "string";
                //typeName = typeName.Remove(typeName.Length - 1);
                string description = item.Value.Split(',')[1];
                props += $"\tpublic {typeName} {item.Key} //{description}\n"
                    + $"\t{{\n"
                    + $"\t\tget;set;\n"
                    + $"\t}}\n";
                // 收集ToString语句
                toStr += $" + \"{item.Key}=\" + {item.Key}";
                // 收集属性名
                propList += $"\"{item.Key}\",";

                // 收集属性类型
                tyList += $"\t\t{{\"{item.Key.Replace(" ", "")}\", \"{typeName}\"}},\n";

                //if(offset != 0)
                setValues += $"\t\tif(raw_data[i][{offset}].ToString() != \"\")\n\t\t\t{item.Key} = ({typeName})Convert.ChangeType(raw_data[i][{offset}].ToString(), typeof({typeName}));\n";
                offset++;
            }


            // 基础运算方法中会调用这些方法来对当前对象的属性进行读取和赋值
            // 依赖属性字符串查表 'propList'
            // tables.GetType().GetProperty("test").GetValue(tables);
            // tables.GetType().GetProperty("test").SetValue(tables, value)
            // 通过属性名获取属性, id是属性在属性列表里的索引
            string getProp(string tar, string id)
            {
                return $"{tar}.GetType().GetProperty(propList[{id}])";
            }
            string getValue(string tar, string id)
            {
                return $"{getProp(tar, id)}.GetValue({tar})";
            }
            string setValue(string tar, string id, string val)
            {
                return $"{getProp(tar, id)}.SetValue({tar}, {val})";
            }


            // 基础运算方法
            string extend = "";
            extend += "\n" +
                // a、添加通用方法：直接提供同数据的所有属性相加，相减，限制相加（提供多一个装备作为限制装备传入）然后相加属性不能大于限制装备的属性没如果大于则等于最大装备属性
                $"\tpublic static {fileName} add({fileName} a, {fileName} b, uint start, uint end, {fileName} limit = null) {{\n" +
                $"\t\tif(a == null || b == null) return null;\n" +
                $"\t\t{fileName} result = new {fileName}();\n" +
                $"\t\tfor(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {{\n" +
                $"\t\t\tvar vb = {getValue("b", "i")};\n" +
                $"\t\t\tvar va = {getValue("a", "i")};\n" +
                $"\t\t\tvar vr = (uint)va + (uint)vb;\n" +
                $"\t\t\t{setValue("result", "i", "vr")};\n" +
                $"\t\t\tif(limit != null) {{\n" +
                $"\t\t\t\tvar vlimit = {getValue("limit", "i")};\n" +
                $"\t\t\t\tif(vr > (uint)vlimit) {{\n" +
                $"\t\t\t\t\t{setValue("result", "i", "vlimit")};\n" +
                $"\t\t\t\t}}\n" +
                $"\t\t\t}}\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n";

            extend += $"\n" +
                $"\tpublic static {fileName} sub({fileName} a, {fileName} b, uint start, uint end) {{\n" +
                $"\t\tif(a == null || b == null) return null;\n" +
                $"\t\t{fileName} result = new {fileName}();\n" +
                $"\t\tfor(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {{\n" +
                $"\t\t\tvar vb = {getValue("b", "i")};\n" +
                $"\t\t\tvar va = {getValue("a", "i")};\n" +
                $"\t\t\t{setValue("result", "i", "(uint)va - (uint)vb")};\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n";

            extend += $"\n" +
                // c、添加一个随机方法，然后传入一件随机属性用装备，一次检查属性不为0的，然后随机一个值出来。然后生成一个json返回。如果传入一件原始装备则，返回json并且把属性添加到原始装备上去 random(item,item=null)
                $"\tpublic static string random({fileName} src, uint i = 0) {{\n" +
                $"\t\tvar tar = {getValue("src", "i")};\n" +
                $"\t\tif((uint)tar == 0) // NOTE:\n" +
                // $"\t\t\t{setValue("src", "i", $"Convert.ChangeType(new Random().Next(), {getProp("src", "i")}.GetType())")};\n" +
                $"\t\t\t{setValue("src", "i", "(uint)(new Random().Next())")};\n" +
                $"\t\treturn JSON.stringify(src);\n" +
                $"\t}}\n";

            /*          */


            extend += $"\n" +
                // d、添加一个比较方法如果a的数值大于装备b，那就返回true
                $"\tpublic static bool larger({fileName} a, {fileName} b, uint i = 0) {{\n" +
                $"\t\treturn (uint){getValue("a", "i")} > (uint){getValue("b", "i")};\n" +
                $"\t}}\n";

            extend += $"\n" +
                // e、添加一个max的方法，然后调用d方法，然后返回处返回最大的属性的那个装备
                $"\tpublic static {fileName} max({fileName} a, {fileName} b, uint i = 0) {{\n" +
                $"\t\tvar vb = {getValue("b", "i")};\n" +
                $"\t\tvar va = {getValue("a", "i")};\n" +
                $"\t\tif((uint)va > (uint)vb)\n" +
                $"\t\t\treturn a;\n" +
                $"\t\treturn b;\n" +
                $"\t}}\n";


            // JsonConvert.DeserializeObject("");

            extend += $"\n" +
                // f、添加一个json方法，传入json然后把属性和相应的值添加到属性中，（json里面就纯粹的属性名字和属性）
                $"\tpublic static {fileName} json({fileName} a, string data) {{\n" +
                $"\t\tvar d = JObject.Parse(data);\n" +
                // $"\t\tvar d = JsonConvert.DeserializeObject<{fileName}>(data);\n" +
                $"\t\tforeach (JProperty p in d.Properties()) {{\n" +
                $"\t\t\tif(p.Name == \"dbID\")\n" +
                $"\t\t\t\tcontinue;\n" +
                $"\t\t\tstring vtype = typeList[p.Name];\n" +
                $"\t\t\tswitch (vtype) {{\n" +
                $"\t\t\t\tcase \"uint\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"int\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"string\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"byte\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"bool\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t}}\n" +
                $"\t\t}}\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n";
            /**/

            extend += $"\n" +
                // i、添加一个方法，然后把单个属性更新到数值当中去。
                $"\tpublic static {fileName} setProperty({fileName} a, uint p, dynamic value) {{\n" +
                $"\t\t{setValue("a", "p", "value")};\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n";
            /*            */

            // 数据对象
            string o = $"class {fileName}\n"
                + $"{{\n"
                + $"{props}\n"
                + $"\tpublic static string getFileName()\n"
                + $"\t{{\n"
                + $"\t\treturn \"{excelName.Replace("\\", "/")}\";\n"
                + $"\t}}\n"
                + $"\tpublic void feed(DataRowCollection raw_data, int i)\n"
                + $"\t{{\n"
                + $"{setValues}"
                + $"\t}}\n"
                // 类型列表
                + $"\tpublic static Dictionary<string, string> typeList = new Dictionary<string, string>() {{\n"
                + $"{tyList}"
                + $"\t}};\n"
                // ToString方法
                + $"\tpublic override string ToString()\n"
                + $"\t{{\n"
                + $"\t\t{toStr};\n"
                + $"\t}}\n"
                // 属性名称列表, 需要按照属性名获取属性值
                + $"\tpublic static string[] propList = {{ {propList} }};"
                // 附加属性
                + $"{extend}"

                + $"}}\n";

            // 数据库相关
            string dbHost = "************";// 数据库地址
            string port = "3307";     // 数据库端口
            string database = "wxh-test";   // 需要映射的表名
            string dbUsr = "root";     // 数据库用户名
            string dbPwd = "Cafe2024!"; // 数据库密码

            // 数据对象
            string r = $"class Db{fileName}: DbContext\n"
                + $"{{\n"
                + $"\tpublic DbSet<{fileName}> {fileName}s {{ get; set; }}\n"   // 表的成员
                + $"\tprotected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)\n"
                + $"\t{{\n"
                + $"\t\tstring connectionString = \"Server={dbHost};Port={port};Database={database};User={dbUsr};Password={dbPwd}\";\n"
                + $"\t\tvar serverVersion = ServerVersion.AutoDetect(connectionString);\n"
                + $"\t\toptionsBuilder.UseMySql(connectionString,serverVersion);\n"
                + $"\t}}\n"
                + $"}}\n";
            return header + o + r;
        }


        static Dictionary<string, string> modelcodes = new Dictionary<string, string>();
        static Dictionary<string, string> modelDescs = new Dictionary<string, string>();

        public static void WriteORM(Dictionary<string, string> excelType, string fileName, string excelName)
        {
            ////设置生成的C#类目录，我这里是直接放到了项目下 
            //string CurDir =  AppConfig.Instance.ormOutPatch;
            ////判断文件夹是否存在,不存在就创建这个文件夹
            //if (!Directory.Exists(CurDir))
            //{
            //    Directory.CreateDirectory(CurDir);
            //}

            ////生成的是c#类   所以需要加上.cs后缀  这里的Pathname为生成类的名字
            //string FilePath = CurDir + fileName + ".cs";
            ////文件覆盖方式添加内容
            //StreamWriter file = new StreamWriter(FilePath, false);
            ////保存数据到文件(将C#类需要的头文件先写入)
            //file.Write(genORM(excelType, fileName, excelName));
            ////关闭文件
            //file.Close();
            ////释放对象
            //file.Dispose();
            //string tmp = ExportClass.genORM(excelType, fileName, excelName);
            //string TestDir = Environment.CurrentDirectory.Replace("\\", "/") + "/AutoCode/ExcelData/";

            //if (!Directory.Exists(TestDir))
            //{
            //    Directory.CreateDirectory(TestDir);
            //}

            //string FilePath = TestDir + fileName + ".cs";

            //StreamWriter file = new StreamWriter(FilePath, false);
            //file.Write(tmp);

            //file.Close();
            ////释放对象
            //file.Dispose();


            var code = BuildCodeSingle(excelType, fileName, excelName);
            modelcodes[fileName] = code;
        }

        public static string tsCoverType(string value)
        {
            switch (value)
            {
                case "int":
                case "uint":
                case "Int":
                case "Uint":
                case "Int16":
                case "Int32":
                case "Int64":
                case "UInt16":
                case "UInt32":
                case "UInt64":
                case "float":
                case "Single":
                case "Byte":
                case "byte":
                    return "number";

                case "bool":
                case "Boolean":
                case "true":
                case "false":
                    return "boolean";
                case "vector3":
                    return "gd3d.math.vector3";
                case "vector4":
                    return "gd3d.math.vector4";
                case "v3Array":
                    {
                        return "Array<gd3d.math.vector3>";
                    }
                case "reshsArray":
                    {
                        return "Array<Array<number>>";
                    }
                case "numArray":
                    {
                        return "Array<number>";
                    }
                default:
                    return "string";

            }
        }

        private static string BuildCodeSingle(Dictionary<string, string> excelType, string fileName, string excelName)
        {

            // 第一个属性通常是ID 作为主键
            string props = "\t[Key]\n";
            // 额外的主键
            props += "\n" +
                "/// <summary>\n" +
                "/// ORM Table ID\n" +
                "/// </summary>\n" +
                "public uint dbID \n"
                + "\t{\n"
                + "\t\tget;set;\n"
                + "\t}\n";

            // ToString method
            string toStr = "return \"\"";

            // Properties
            string propList = "";


            string setValues = "";

            // 属性类型字典 JSON需要
            string tyList = "";


            int offset = 0;
            // Properties
            foreach (var item in excelType)
            {
                string typeName = item.Value.Split(',')[0];
                if (item.Key == "")
                    continue;
                // TODO: handle array
                if (typeName == "date")
                    typeName = "long";

                if (isArray(typeName))
                    typeName = "string[]";
                if (typeName == "vector3" || typeName == "vector4")
                    typeName = "string";
                //typeName = typeName.Remove(typeName.Length - 1);
                string description = item.Value.Split(',')[1];
                string newdes = description;
                if (description.IndexOf("\n") != -1)
                {
                    newdes = "";
                    string[] dess = description.Split("\n");
                    foreach (var de in dess)
                    {

                        newdes += $"\t/// {de}\n";
                    }
                }

                // 判断是否应该为可空类型：名字不包含 "id" 的字段设为可空
                bool isNullable = !item.Key.ToLower().Contains("id");
                string nullableTypeName = typeName;

                // 如果是值类型且应该为可空，添加 ? 后缀
                if (isNullable && IsValueType(typeName))
                {
                    nullableTypeName = typeName + "?";
                }

                props += $"\n" +
                    $"\t/// <summary>\n" +




                    $"\t/// {newdes}\n" +




                    $"\t/// </summary>\n"
                    + $"\tpublic {nullableTypeName} {item.Key}\n"
                    + $"\t{{\n"
                    + $"\t\tget;set;\n"
                    + $"\t}}\n";
                // 收集ToString语句
                toStr += $" + \"{item.Key}=\" + {item.Key}";
                // 收集属性名
                propList += $"\"{item.Key}\",";

                // 收集属性类型
                tyList += $"\t\t{{\"{item.Key.Replace(" ", "")}\", \"{typeName}\"}},\n";

                //if(offset != 0)
                // 对于可空类型，需要特殊处理赋值逻辑
                if (isNullable && IsValueType(typeName))
                {
                    setValues += $"\t\tif(raw_data[i][{offset}].ToString() != \"\")\n\t\t\t{item.Key} = ({typeName})Convert.ChangeType(raw_data[i][{offset}].ToString(), typeof({typeName}));\n\t\telse\n\t\t\t{item.Key} = null;\n";
                }
                else
                {
                    setValues += $"\t\tif(raw_data[i][{offset}].ToString() != \"\")\n\t\t\t{item.Key} = ({typeName})Convert.ChangeType(raw_data[i][{offset}].ToString(), typeof({typeName}));\n";
                }
                offset++;
            }


            // 基础运算方法中会调用这些方法来对当前对象的属性进行读取和赋值
            // 依赖属性字符串查表 'propList'
            // tables.GetType().GetProperty("test").GetValue(tables);
            // tables.GetType().GetProperty("test").SetValue(tables, value)
            // 通过属性名获取属性, id是属性在属性列表里的索引
            string getProp(string tar, string id)
            {
                return $"{tar}.GetType().GetProperty(propList[{id}])";
            }
            string getValue(string tar, string id)
            {
                return $"{getProp(tar, id)}.GetValue({tar})";
            }
            string setValue(string tar, string id, string val)
            {
                return $"{getProp(tar, id)}.SetValue({tar}, {val})";
            }


            // 基础运算方法
            string extend = "";
            extend += "\n" +
                // a、添加通用方法：直接提供同数据的所有属性相加，相减，限制相加（提供多一个装备作为限制装备传入）然后相加属性不能大于限制装备的属性没如果大于则等于最大装备属性
                $"\tpublic static {fileName} add({fileName} a, {fileName} b, uint start, uint end, {fileName} limit = null) {{\n" +
                $"\t\tif(a == null || b == null) return null;\n" +
                $"\t\t{fileName} result = new {fileName}();\n" +
                $"\t\tfor(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {{\n" +
                $"\t\t\tvar vb = {getValue("b", "i")};\n" +
                $"\t\t\tvar va = {getValue("a", "i")};\n" +
                $"\t\t\tvar vr = (uint)va + (uint)vb;\n" +
                $"\t\t\t{setValue("result", "i", "vr")};\n" +
                $"\t\t\tif(limit != null) {{\n" +
                $"\t\t\t\tvar vlimit = {getValue("limit", "i")};\n" +
                $"\t\t\t\tif(vr > (uint)vlimit) {{\n" +
                $"\t\t\t\t\t{setValue("result", "i", "vlimit")};\n" +
                $"\t\t\t\t}}\n" +
                $"\t\t\t}}\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n";

            extend += $"\n" +
                $"\tpublic static {fileName} sub({fileName} a, {fileName} b, uint start, uint end) {{\n" +
                $"\t\tif(a == null || b == null) return null;\n" +
                $"\t\t{fileName} result = new {fileName}();\n" +
                $"\t\tfor(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {{\n" +
                $"\t\t\tvar vb = {getValue("b", "i")};\n" +
                $"\t\t\tvar va = {getValue("a", "i")};\n" +
                $"\t\t\t{setValue("result", "i", "(uint)va - (uint)vb")};\n" +
                $"\t\t}}\n" +
                $"\t\treturn result;\n" +
                $"\t}}\n";

            /*
            extend += $"\n" +
                // c、添加一个随机方法，然后传入一件随机属性用装备，一次检查属性不为0的，然后随机一个值出来。然后生成一个json返回。如果传入一件原始装备则，返回json并且把属性添加到原始装备上去 random(item,item=null)
                $"\tpublic static string random({fileName} src, uint i = 0) {{\n" +
                $"\t\tvar tar = {getValue("src", "i")};\n" +
                $"\t\tif((uint)tar == 0) // NOTE:\n" +
                // $"\t\t\t{setValue("src", "i", $"Convert.ChangeType(new Random().Next(), {getProp("src", "i")}.GetType())")};\n" +
                $"\t\t\t{setValue("src", "i", "(uint)(new Random().Next())")};\n" +
                $"\t\treturn JSON.stringify(src);\n" +
                $"\t}}\n";

                      */


            extend += $"\n" +
                // d、添加一个比较方法如果a的数值大于装备b，那就返回true
                $"\tpublic static bool larger({fileName} a, {fileName} b, uint i = 0) {{\n" +
                $"\t\treturn (uint){getValue("a", "i")} > (uint){getValue("b", "i")};\n" +
                $"\t}}\n";

            extend += $"\n" +
                // e、添加一个max的方法，然后调用d方法，然后返回处返回最大的属性的那个装备
                $"\tpublic static {fileName} max({fileName} a, {fileName} b, uint i = 0) {{\n" +
                $"\t\tvar vb = {getValue("b", "i")};\n" +
                $"\t\tvar va = {getValue("a", "i")};\n" +
                $"\t\tif((uint)va > (uint)vb)\n" +
                $"\t\t\treturn a;\n" +
                $"\t\treturn b;\n" +
                $"\t}}\n";


            // JsonConvert.DeserializeObject("");

            extend += $"\n" +
                // f、添加一个json方法，传入json然后把属性和相应的值添加到属性中，（json里面就纯粹的属性名字和属性）
                $"\tpublic static {fileName} json({fileName} a, string data) {{\n" +
                $"\t\tvar d = JObject.Parse(data);\n" +
                // $"\t\tvar d = JsonConvert.DeserializeObject<{fileName}>(data);\n" +
                $"\t\tforeach (JProperty p in d.Properties()) {{\n" +
                $"\t\t\tif(p.Name == \"dbID\")\n" +
                $"\t\t\t\tcontinue;\n" +
                $"\t\t\tstring vtype = typeList[p.Name];\n" +
                $"\t\t\tswitch (vtype) {{\n" +
                $"\t\t\t\tcase \"uint\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"int\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"string\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"byte\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t\tcase \"bool\":\n" +
                $"\t\t\t\t\ta.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));\n" +
                $"\t\t\t\t\tbreak;\n" +
                $"\t\t\t}}\n" +
                $"\t\t}}\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n";
            /**/

            extend += $"\n" +
                // i、添加一个方法，然后把单个属性更新到数值当中去。
                $"\tpublic static {fileName} setProperty({fileName} a, uint p, dynamic value) {{\n" +
                $"\t\t{setValue("a", "p", "value")};\n" +
                $"\t\treturn a;\n" +
                $"\t}}\n";

            extend += $"\n" +
                //读二进制文件中的行列数据，每行生成一个本对象实例，最后给DbContext上传服务器
                $"\tpublic static List<{fileName}> readAllData(){{\n" +
                $"\t\tstring excelJsonPath = AppContext.BaseDirectory + \"excelData/{fileName}.jason\";\n" +
                $"\n" +
                $"\t\tif(!File.Exists(excelJsonPath)){{\n" +
                $"\t\t\tConsole.WriteLine(\"{fileName}的二进制文件未找到\");\n" +
                $"\t\t\treturn null;\n" +
                $"\t\t}}\n" +
                $"\n" +
                $"\t\tFileStream fs = File.OpenRead(excelJsonPath);\n" +
                $"\t\tBinaryReader br = new BinaryReader(fs);\n" +
                $"\n" +
                $"\t\tint configNum = br.ReadInt32();\n" +
                $"\t\tfor (int i = 0; i < configNum; i++){{\n" +
                $"\t\t\tbyte[] keyArray = br.ReadBytes(br.ReadUInt16());\n" +
                $"\n" +
                $"\t\t\tbyte[] valueArray = br.ReadBytes(br.ReadUInt16());\n" +
                $"\t\t}}" +
                $"\n" +
                $"\t\tList<{fileName}> list = new List<{fileName}>();\n" +
                $"\n" +
                $"\t\tint row = br.ReadInt32();\n" +
                $"\t\tint col = br.ReadInt32();\n" +
                $"\n" +
                $"\t\tfor (int i = 0; i < row; i++){{\n" +
                $"\t\t\t{fileName} obj = new {fileName}();\n";
            extend += BuildPropertyToReadBinary(excelType, fileName);
            extend += "\t\t\t\n" +
                "\t\t\tlist.Add(obj);\n" +
                "\t\t}\n" +
                "\n" +
                "\t\tif(br!=null){\n" +
                "\t\t\tbr.Dispose();\n" +
                "\t\t}\n" +
                "\t\tif(fs!=null){\n" +
                "\t\t\tfs.Dispose();\n" +
                "\t\t}\n" +
                "\t\treturn list;\n" +
                "\t}\n";

            // 添加获取所有数据的方法
            extend += $"\n" +
                $"\t/// <summary>\n" +
                $"\t/// 获取所有数据，通过 ORMTables.Instance 单例访问数据库\n" +
                $"\t/// </summary>\n" +
                $"\t/// <returns>返回所有 {fileName} 数据的列表</returns>\n" +
                $"\tpublic static List<{fileName}> getAllDataToList(){{\n" +
                $"\t\ttry{{\n" +
                $"\t\t\tvar db = ORMTables.Instance;\n" +
                $"\t\t\treturn db.{fileName}s.ToList();\n" +
                $"\t\t}}\n" +
                $"\t\tcatch (Exception ex){{\n" +
                $"\t\t\tConsole.WriteLine($\"获取 {fileName} 所有数据时发生错误: {{ex.Message}}\");\n" +
                $"\t\t\treturn new List<{fileName}>();\n" +
                $"\t\t}}\n" +
                $"\t}}\n";

            // 添加根据字段查询数据的方法
            extend += $"\n" +
                $"\t/// <summary>\n" +
                $"\t/// 根据指定字段和值查询数据，通过 ORMTables.Instance 单例访问数据库\n" +
                $"\t/// </summary>\n" +
                $"\t/// <param name=\"fieldName\">字段名称</param>\n" +
                $"\t/// <param name=\"value\">字段值</param>\n" +
                $"\t/// <returns>返回匹配条件的 {fileName} 数据列表</returns>\n" +
                $"\tpublic static List<{fileName}> getDataToKey(string fieldName, object value){{\n" +
                $"\t\ttry{{\n" +
                $"\t\t\tvar db = ORMTables.Instance;\n" +
                $"\t\t\tvar query = db.{fileName}s.AsQueryable();\n" +
                $"\t\t\t\n" +
                $"\t\t\t// 使用反射根据字段名进行查询\n" +
                $"\t\t\tvar property = typeof({fileName}).GetProperty(fieldName);\n" +
                $"\t\t\tif (property == null){{\n" +
                $"\t\t\t\tConsole.WriteLine($\"字段 {{fieldName}} 在 {fileName} 类中不存在\");\n" +
                $"\t\t\t\treturn new List<{fileName}>();\n" +
                $"\t\t\t}}\n" +
                $"\t\t\t\n" +
                $"\t\t\t// 构建 LINQ 表达式进行查询\n" +
                $"\t\t\tvar parameter = System.Linq.Expressions.Expression.Parameter(typeof({fileName}), \"x\");\n" +
                $"\t\t\tvar propertyAccess = System.Linq.Expressions.Expression.Property(parameter, fieldName);\n" +
                $"\t\t\tvar constant = System.Linq.Expressions.Expression.Constant(value);\n" +
                $"\t\t\tvar equality = System.Linq.Expressions.Expression.Equal(propertyAccess, constant);\n" +
                $"\t\t\tvar lambda = System.Linq.Expressions.Expression.Lambda<Func<{fileName}, bool>>(equality, parameter);\n" +
                $"\t\t\t\n" +
                $"\t\t\treturn query.Where(lambda).ToList();\n" +
                $"\t\t}}\n" +
                $"\t\tcatch (Exception ex){{\n" +
                $"\t\t\tConsole.WriteLine($\"根据字段 {{fieldName}} 查询 {fileName} 数据时发生错误: {{ex.Message}}\");\n" +
                $"\t\t\treturn new List<{fileName}>();\n" +
                $"\t\t}}\n" +
                $"\t}}\n";

            // 为每个字段生成专门的查询方法
            foreach (var field in excelType)
            {
                if (field.Key == "" || field.Key == "dbID") continue;

                string fieldName = field.Key;
                string fieldType = field.Value.Split(',')[0];

                // 处理特殊类型
                if (fieldType == "date") fieldType = "long";
                if (isArray(fieldType)) fieldType = "string[]";
                if (fieldType == "vector3" || fieldType == "vector4") fieldType = "string";

                // 判断是否为可空类型
                bool isNullable = !fieldName.ToLower().Contains("id");
                string parameterType = fieldType;
                if (isNullable && IsValueType(fieldType))
                {
                    parameterType = fieldType + "?";
                }

                // 生成单条数据查询方法
                extend += $"\n" +
                    $"\t/// <summary>\n" +
                    $"\t/// 根据 {fieldName} 字段查询单条数据\n" +
                    $"\t/// </summary>\n" +
                    $"\t/// <param name=\"{fieldName}\">查询值</param>\n" +
                    $"\t/// <returns>返回匹配的第一条 {fileName} 数据，如果没有找到则返回 null</returns>\n" +
                    $"\tpublic static {fileName} getBy{fieldName}({parameterType} {fieldName}){{\n" +
                    $"\t\ttry{{\n" +
                    $"\t\t\tvar db = ORMTables.Instance;\n" +
                    $"\t\t\treturn db.{fileName}s.FirstOrDefault(x => x.{fieldName} == {fieldName});\n" +
                    $"\t\t}}\n" +
                    $"\t\tcatch (Exception ex){{\n" +
                    $"\t\t\tConsole.WriteLine($\"根据 {fieldName} 查询 {fileName} 单条数据时发生错误: {{ex.Message}}\");\n" +
                    $"\t\t\treturn null;\n" +
                    $"\t\t}}\n" +
                    $"\t}}\n";

                // 生成多条数据查询方法
                extend += $"\n" +
                    $"\t/// <summary>\n" +
                    $"\t/// 根据 {fieldName} 字段查询多条数据\n" +
                    $"\t/// </summary>\n" +
                    $"\t/// <param name=\"{fieldName}\">查询值</param>\n" +
                    $"\t/// <returns>返回匹配的所有 {fileName} 数据列表</returns>\n" +
                    $"\tpublic static List<{fileName}> getListBy{fieldName}({parameterType} {fieldName}){{\n" +
                    $"\t\ttry{{\n" +
                    $"\t\t\tvar db = ORMTables.Instance;\n" +
                    $"\t\t\treturn db.{fileName}s.Where(x => x.{fieldName} == {fieldName}).ToList();\n" +
                    $"\t\t}}\n" +
                    $"\t\tcatch (Exception ex){{\n" +
                    $"\t\t\tConsole.WriteLine($\"根据 {fieldName} 查询 {fileName} 多条数据时发生错误: {{ex.Message}}\");\n" +
                    $"\t\t\treturn new List<{fileName}>();\n" +
                    $"\t\t}}\n" +
                    $"\t}}\n";
            }



            /*            */
            modelDescs[fileName] = Path.GetFileName(excelName).Replace(".xlsx", "").Replace(".xls", "");
            var idType = excelType["id"].Split(",")[0];
            var extendsInc = idType == "string" ? "IDBStrBase" : "IDBNumBase";
            // 数据对象
            string classStr = $"\n" +
                $"/// <summary>\n" +
                $"/// {modelDescs[fileName]}\n" +
                $"/// </summary>\n" +
                $"[Table(\"{fileName.ToLower()}s\")]\n" +
                $"public class {fileName}\n"
                + $"{{\n"
                + $"{props}\n"
                + $"\tpublic static string getFileName()\n"
                + $"\t{{\n"
                + $"\t\treturn \"{excelName.Replace("\\", "/")}\";\n"
                + $"\t}}\n"
                + $"\tpublic void feed(DataRowCollection raw_data, int i)\n"
                + $"\t{{\n"
                + $"{setValues}"
                + $"\t}}\n"
                // 类型列表
                + $"\tpublic static Dictionary<string, string> typeList = new Dictionary<string, string>() {{\n"
                + $"{tyList}"
                + $"\t}};\n"
                // ToString方法
                + $"\tpublic override string ToString()\n"
                + $"\t{{\n"
                + $"\t\t{toStr};\n"
                + $"\t}}\n"
                // 属性名称列表, 需要按照属性名获取属性值
                + $"\tpublic static string[] propList = {{ {propList} }};"
                // 附加属性
                + $"{extend}"

                + $"}}\n";
            return classStr;
        }

        private static string BuildPropertyToReadBinary(Dictionary<string, string> excelType, string fileName)
        {
            string ret = "";
            foreach (var item in excelType)
            {
                if (item.Value.Split(",")[0] == "string")
                {
                    ret += ("\t\t\tobj." + item.Key + "= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));\n");
                }
                else if (item.Value.Split(",")[0] == "uint")
                {
                    ret += ("\t\t\tobj." + item.Key + "= br.ReadUInt32();\n");
                }
                else if (item.Value.Split(",")[0] == "int")
                {
                    ret += ("\t\t\tobj." + item.Key + "= br.ReadInt32();\n");
                }
                else if (item.Value.Split(",")[0] == "date")
                {
                    ret += ("\t\t\tobj." + item.Key + "= br.ReadInt64();\n");
                }
                else if (item.Value.Split(",")[0] == "byte")
                {
                    ret += ("\t\t\tobj." + item.Key + "= br.ReadByte();\n");
                }
                else if (item.Value.Split(",")[0] == "string[]")
                {
                    ret += ("\t\t\tobj." + item.Key + "= new string[1];\n");
                    ret += ($"\t\t\tint {item.Key}Num=br.ReadInt32();\n");
                    ret += ($"\t\t\tfor (int tmp = 0; tmp < {item.Key}Num; tmp++){{\n");
                    ret += ($"\t\t\t\tstring tmpStr = System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16())) + (tmp < {item.Key}Num - 1 ? \",\" : \"\");\n");
                    ret += ($"\t\t\t\tobj.{item.Key}[0]+=tmpStr;\n");
                    ret += "\t\t\t}\n";
                }
                else if (item.Value.Split(",")[0] == "bool")
                {
                    ret += ("\t\t\tobj." + item.Key + "= br.ReadBoolean();\n");
                }
                else if (item.Value.Split(",")[0] == "ulong")
                {
                    ret += ("\t\t\tobj." + item.Key + "= br.ReadUInt64();\n");
                }
                else if (item.Value.Split(",")[0] == "long")
                {
                    ret += ("\t\t\tobj." + item.Key + "= br.ReadInt64();\n");
                }
                else
                {
                    Console.WriteLine("有特殊类型没有写入ORM实体类");
                }
            }
            return ret;
        }

        private static StringBuilder BuildTools(string indentChar)
        {
            StringBuilder sb = new StringBuilder();


            sb.AppendLine($"{indentChar}\t/// <summary>");
            sb.AppendLine($"{indentChar}\t/// 数据库里的表");
            sb.AppendLine($"{indentChar}\t/// 这里的属性如果有修改需要清空数据库, 或者新建一个数据库 (数据库只要是空的就行)");
            sb.AppendLine($"{indentChar}\t/// 如果只是表里的数据有修改, 只需要清空对应的表, 重启服务器会重新填数据");
            sb.AppendLine($"{indentChar}\t/// </summary>");
            sb.AppendLine($"{indentChar}\tpublic class ORMTables: DBBase");
            sb.AppendLine($"{indentChar}\t{{");

            // 添加单例模式实现
            sb.AppendLine($"{indentChar}\t\t// 私有静态实例变量");
            sb.AppendLine($"{indentChar}\t\tprivate static ORMTables _instance;");
            sb.AppendLine($"{indentChar}\t\t// 线程安全锁对象");
            sb.AppendLine($"{indentChar}\t\tprivate static readonly object _lock = new object();");
            sb.AppendLine($"");
            sb.AppendLine($"{indentChar}\t\t/// <summary>");
            sb.AppendLine($"{indentChar}\t\t/// 公共静态属性，用于访问单例实例");
            sb.AppendLine($"{indentChar}\t\t/// </summary>");
            sb.AppendLine($"{indentChar}\t\tpublic static ORMTables Instance");
            sb.AppendLine($"{indentChar}\t\t{{");
            sb.AppendLine($"{indentChar}\t\t\tget");
            sb.AppendLine($"{indentChar}\t\t\t{{");
            sb.AppendLine($"{indentChar}\t\t\t\t// 双重检查锁定模式确保线程安全");
            sb.AppendLine($"{indentChar}\t\t\t\tif (_instance == null)");
            sb.AppendLine($"{indentChar}\t\t\t\t{{");
            sb.AppendLine($"{indentChar}\t\t\t\t\tlock (_lock)");
            sb.AppendLine($"{indentChar}\t\t\t\t\t{{");
            sb.AppendLine($"{indentChar}\t\t\t\t\t\tif (_instance == null)");
            sb.AppendLine($"{indentChar}\t\t\t\t\t\t{{");
            sb.AppendLine($"{indentChar}\t\t\t\t\t\t\t_instance = new ORMTables();");
            sb.AppendLine($"{indentChar}\t\t\t\t\t\t}}");
            sb.AppendLine($"{indentChar}\t\t\t\t\t}}");
            sb.AppendLine($"{indentChar}\t\t\t\t}}");
            sb.AppendLine($"{indentChar}\t\t\t\treturn _instance;");
            sb.AppendLine($"{indentChar}\t\t\t}}");
            sb.AppendLine($"{indentChar}\t\t}}");
            sb.AppendLine($"");

            foreach (var item in modelDescs)
            {
                sb.AppendLine($"{indentChar}\t/// <summary>");

                sb.AppendLine($"{indentChar}\t/// {item.Value}");

                sb.AppendLine($"{indentChar}\t/// <summary>");
                sb.AppendLine($"{indentChar}\tpublic DbSet<{item.Key}> {item.Key}s {{ get; set; }}");
            }


            
            sb.AppendLine($"{indentChar}\t/// <summary>");
            sb.AppendLine($"{indentChar}\t/// 读取所有数据到数据库");
            sb.AppendLine($"{indentChar}\t/// </summary>");
            sb.AppendLine($"{indentChar}\tpublic void readAllData(){{");
            sb.AppendLine($"{indentChar}\t\tvar db = ORMTables.Instance;");
            foreach (var item in modelDescs)
            {
                sb.AppendLine($"{indentChar}\t\tdb.{item.Key}s.AddRange({item.Key}.readAllData().ToArray());");
            }

            sb.AppendLine($"{indentChar}\t\tdb.SaveChanges();");
            sb.AppendLine($"{indentChar}\t\tdb.Dispose();");
            sb.AppendLine($"{indentChar}\t\tConsole.WriteLine(\"数据上传数据库完毕\");");

            sb.AppendLine($"{indentChar}\t}}");

            // 添加清空所有数据库内容的方法
            sb.AppendLine($"{indentChar}\t/// <summary>");
            sb.AppendLine($"{indentChar}\t/// 清空数据库中所有表的数据");
            sb.AppendLine($"{indentChar}\t/// </summary>");
            sb.AppendLine($"{indentChar}\tpublic void ClearAllData(){{");
            sb.AppendLine($"{indentChar}\t\tvar db = ORMTables.Instance;");
            sb.AppendLine($"{indentChar}\t\ttry{{");
            foreach (var item in modelDescs)
            {
                sb.AppendLine($"{indentChar}\t\t\tdb.{item.Key}s.ExecuteDelete();");
            }
            sb.AppendLine($"{indentChar}\t\t\tdb.SaveChanges();");
            sb.AppendLine($"{indentChar}\t\t\tConsole.WriteLine(\"所有数据库表数据已清空\");");
            sb.AppendLine($"{indentChar}\t\t}}");
            sb.AppendLine($"{indentChar}\t\tcatch (Exception ex){{");
            sb.AppendLine($"{indentChar}\t\t\tConsole.WriteLine($\"清空数据库数据时发生错误: {{ex.Message}}\");");
            sb.AppendLine($"{indentChar}\t\t}}");
            sb.AppendLine($"{indentChar}\t\tfinally{{");
            sb.AppendLine($"{indentChar}\t\t\tdb.Dispose();");
            sb.AppendLine($"{indentChar}\t\t}}");
            sb.AppendLine($"{indentChar}\t}}");

            sb.AppendLine($"{indentChar}\t}}");

            return sb;
        }
        public static void Start()
        {

        }

        public static void End()
        {

            //设置生成的C#类目录，我这里是直接放到了项目下 
            string CurDir = Directory.GetParent(AppContext.BaseDirectory).Parent.Parent.Parent.FullName;//AppConfig.Instance.ormOutPatch;
            //判断文件夹是否存在,不存在就创建这个文件夹
            if (!Directory.Exists(CurDir))
            {
                Directory.CreateDirectory(CurDir);
            }



            StringBuilder sb = new StringBuilder();
            sb.AppendLine("using System;");
            sb.AppendLine("using Microsoft.EntityFrameworkCore;");
            sb.AppendLine("using System.ComponentModel.DataAnnotations;");
            sb.AppendLine("using System.Data;");
            sb.AppendLine("using System.Collections.Generic;");
            sb.AppendLine("using Newtonsoft.Json.Linq;");
            sb.AppendLine("using ToolsService;");
            sb.AppendLine("using System.ComponentModel.DataAnnotations.Schema;");
            sb.AppendLine("using GameServer.ORM;");
            sb.AppendLine("using GameServer.ExcelData;");
            sb.AppendLine("using System.Linq;");
            sb.AppendLine("using System.Linq.Expressions;");
            sb.AppendLine("using System.Reflection;");
            sb.AppendLine("");
            sb.AppendLine("");
            sb.AppendLine("namespace ExcelToData");
            sb.AppendLine("{");
            //sb.AppendLine("public interface IDBNumBase");
            //sb.AppendLine("{");
            //sb.AppendLine("\tpublic uint dbID");
            //sb.AppendLine("\t{");
            //sb.AppendLine("\t\tget; set;");
            //sb.AppendLine("\t}");
            //sb.AppendLine("\tpublic uint id");
            //sb.AppendLine("\t{");
            //sb.AppendLine("\t\tget; set;");
            //sb.AppendLine("\t}");
            //sb.AppendLine("}");
            //sb.AppendLine("public interface IDBStrBase");
            //sb.AppendLine("{");
            //sb.AppendLine("\tpublic uint dbID");
            //sb.AppendLine("\t{");
            //sb.AppendLine("\t\tget; set;");
            //sb.AppendLine("\t}");
            //sb.AppendLine("\tpublic string id");
            //sb.AppendLine("\t{");
            //sb.AppendLine("\t\tget; set;");
            //sb.AppendLine("\t}");
            //sb.AppendLine("}");
            foreach (var item in modelcodes)
                sb.AppendLine(item.Value);

            sb.Append(BuildTools("\t"));

            sb.AppendLine("}");

            string FilePath = CurDir + "\\ORMModel.cs";

            if (File.Exists(FilePath))
            {
                File.Delete(FilePath);
            }

            using (StreamWriter file = new StreamWriter(FilePath, false))
            {
                file.Write(sb);
            }

        }
    }
}
