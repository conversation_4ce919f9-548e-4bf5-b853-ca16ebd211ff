using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using ExcelToData;
using Microsoft.EntityFrameworkCore;

namespace SaveDataService
{
    /// <summary>
    /// 账号管理类 - 提供注册、登录、忘记密码等账号相关功能
    /// </summary>
    public class AccountManage
    {
        #region 静态字段

        /// <summary>
        /// 验证码存储（简单内存存储，实际项目中应该使用Redis等缓存）
        /// </summary>
        private static readonly Dictionary<string, VerificationCodeInfo> _verificationCodes = new Dictionary<string, VerificationCodeInfo>();

        #endregion

        #region 常量定义
        
        /// <summary>
        /// 密码最小长度
        /// </summary>
        private const int MIN_PASSWORD_LENGTH = 6;
        
        /// <summary>
        /// 密码最大长度
        /// </summary>
        private const int MAX_PASSWORD_LENGTH = 20;
        
        /// <summary>
        /// 令牌过期时间（小时）
        /// </summary>
        private const int TOKEN_EXPIRE_HOURS = 24;
        
        /// <summary>
        /// 最大登录失败次数
        /// </summary>
        private const int MAX_LOGIN_ATTEMPTS = 5;
        
        #endregion

        #region 注册功能
        
        /// <summary>
        /// 用户名注册
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="email">邮箱（可选）</param>
        /// <param name="mobile">手机号（可选）</param>
        /// <returns>注册结果</returns>
        public static RegisterResult RegisterByUsername(string username, string password, string email = null, string mobile = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username))
                    return new RegisterResult { Success = false, Message = "用户名不能为空" };
                
                if (string.IsNullOrWhiteSpace(password))
                    return new RegisterResult { Success = false, Message = "密码不能为空" };
                
                // 验证密码强度
                var passwordValidation = ValidatePassword(password);
                if (!passwordValidation.IsValid)
                    return new RegisterResult { Success = false, Message = passwordValidation.Message };
                
                // 验证邮箱格式
                if (!string.IsNullOrWhiteSpace(email) && !IsValidEmail(email))
                    return new RegisterResult { Success = false, Message = "邮箱格式不正确" };
                
                // 验证手机号格式
                if (!string.IsNullOrWhiteSpace(mobile) && !IsValidMobile(mobile))
                    return new RegisterResult { Success = false, Message = "手机号格式不正确" };
                
                // 检查用户名是否已存在
                var existingUser = Account.getByusrname(username);
                if (existingUser != null)
                    return new RegisterResult { Success = false, Message = "用户名已存在" };
                
                // 检查邮箱是否已存在
                if (!string.IsNullOrWhiteSpace(email))
                {
                    var existingEmail = Account.getByemail(email);
                    if (existingEmail != null)
                        return new RegisterResult { Success = false, Message = "邮箱已被注册" };
                }
                
                // 检查手机号是否已存在
                if (!string.IsNullOrWhiteSpace(mobile))
                {
                    var existingMobile = Account.getBymobile(mobile);
                    if (existingMobile != null)
                        return new RegisterResult { Success = false, Message = "手机号已被注册" };
                }
                
                // 创建新账号
                var newAccount = new Account
                {
                    id = GenerateUniqueId(),
                    usrname = username,
                    password = HashPassword(password),
                    email = email,
                    mobile = mobile,
                    two_factor_auth = false,
                    deleted = false,
                    currentLogin = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    lastLogin = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };
                
                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Add(newAccount);
                db.SaveChanges();
                
                return new RegisterResult 
                { 
                    Success = true, 
                    Message = "注册成功", 
                    AccountId = newAccount.id,
                    AccessToken = GenerateAccessToken(newAccount.id)
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"注册时发生错误: {ex.Message}");
                return new RegisterResult { Success = false, Message = "注册失败，请稍后重试" };
            }
        }
        
        /// <summary>
        /// 邮箱注册
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <param name="password">密码</param>
        /// <param name="username">用户名（可选）</param>
        /// <returns>注册结果</returns>
        public static RegisterResult RegisterByEmail(string email, string password, string username = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(email))
                    return new RegisterResult { Success = false, Message = "邮箱不能为空" };
                
                if (!IsValidEmail(email))
                    return new RegisterResult { Success = false, Message = "邮箱格式不正确" };
                
                // 如果没有提供用户名，使用邮箱前缀作为用户名
                if (string.IsNullOrWhiteSpace(username))
                {
                    username = email.Split('@')[0];
                    // 确保用户名唯一
                    var counter = 1;
                    var originalUsername = username;
                    while (Account.getByusrname(username) != null)
                    {
                        username = $"{originalUsername}{counter}";
                        counter++;
                    }
                }
                
                return RegisterByUsername(username, password, email);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"邮箱注册时发生错误: {ex.Message}");
                return new RegisterResult { Success = false, Message = "注册失败，请稍后重试" };
            }
        }
        
        /// <summary>
        /// 手机号注册
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <param name="password">密码</param>
        /// <param name="username">用户名（可选）</param>
        /// <returns>注册结果</returns>
        public static RegisterResult RegisterByMobile(string mobile, string password, string username = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(mobile))
                    return new RegisterResult { Success = false, Message = "手机号不能为空" };
                
                if (!IsValidMobile(mobile))
                    return new RegisterResult { Success = false, Message = "手机号格式不正确" };
                
                // 如果没有提供用户名，使用手机号作为用户名
                if (string.IsNullOrWhiteSpace(username))
                {
                    username = mobile;
                    // 确保用户名唯一
                    var counter = 1;
                    var originalUsername = username;
                    while (Account.getByusrname(username) != null)
                    {
                        username = $"{originalUsername}_{counter}";
                        counter++;
                    }
                }
                
                return RegisterByUsername(username, password, null, mobile);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"手机号注册时发生错误: {ex.Message}");
                return new RegisterResult { Success = false, Message = "注册失败，请稍后重试" };
            }
        }
        
        #endregion

        #region 登录功能

        /// <summary>
        /// 用户名登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="clientIp">客户端IP</param>
        /// <returns>登录结果</returns>
        public static LoginResult LoginByUsername(string username, string password, string clientIp = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username))
                    return new LoginResult { Success = false, Message = "用户名不能为空" };

                if (string.IsNullOrWhiteSpace(password))
                    return new LoginResult { Success = false, Message = "密码不能为空" };

                // 查找用户
                var account = Account.getByusrname(username);
                if (account == null)
                    return new LoginResult { Success = false, Message = "用户名或密码错误" };

                // 检查账号是否被删除
                if (account.deleted == true)
                    return new LoginResult { Success = false, Message = "账号已被禁用" };

                // 验证密码
                if (!VerifyPassword(password, account.password))
                    return new LoginResult { Success = false, Message = "用户名或密码错误" };

                // 更新登录信息
                UpdateLoginInfo(account, clientIp);

                // 生成访问令牌
                var accessToken = GenerateAccessToken(account.id);

                return new LoginResult
                {
                    Success = true,
                    Message = "登录成功",
                    Account = account,
                    AccessToken = accessToken
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"登录时发生错误: {ex.Message}");
                return new LoginResult { Success = false, Message = "登录失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 邮箱登录
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <param name="password">密码</param>
        /// <param name="clientIp">客户端IP</param>
        /// <returns>登录结果</returns>
        public static LoginResult LoginByEmail(string email, string password, string clientIp = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(email))
                    return new LoginResult { Success = false, Message = "邮箱不能为空" };

                if (!IsValidEmail(email))
                    return new LoginResult { Success = false, Message = "邮箱格式不正确" };

                // 查找用户
                var account = Account.getByemail(email);
                if (account == null)
                    return new LoginResult { Success = false, Message = "邮箱或密码错误" };

                // 检查账号是否被删除
                if (account.deleted == true)
                    return new LoginResult { Success = false, Message = "账号已被禁用" };

                // 验证密码
                if (!VerifyPassword(password, account.password))
                    return new LoginResult { Success = false, Message = "邮箱或密码错误" };

                // 更新登录信息
                UpdateLoginInfo(account, clientIp);

                // 生成访问令牌
                var accessToken = GenerateAccessToken(account.id);

                return new LoginResult
                {
                    Success = true,
                    Message = "登录成功",
                    Account = account,
                    AccessToken = accessToken
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"邮箱登录时发生错误: {ex.Message}");
                return new LoginResult { Success = false, Message = "登录失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 手机号登录
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <param name="password">密码</param>
        /// <param name="clientIp">客户端IP</param>
        /// <returns>登录结果</returns>
        public static LoginResult LoginByMobile(string mobile, string password, string clientIp = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(mobile))
                    return new LoginResult { Success = false, Message = "手机号不能为空" };

                if (!IsValidMobile(mobile))
                    return new LoginResult { Success = false, Message = "手机号格式不正确" };

                // 查找用户
                var account = Account.getBymobile(mobile);
                if (account == null)
                    return new LoginResult { Success = false, Message = "手机号或密码错误" };

                // 检查账号是否被删除
                if (account.deleted == true)
                    return new LoginResult { Success = false, Message = "账号已被禁用" };

                // 验证密码
                if (!VerifyPassword(password, account.password))
                    return new LoginResult { Success = false, Message = "手机号或密码错误" };

                // 更新登录信息
                UpdateLoginInfo(account, clientIp);

                // 生成访问令牌
                var accessToken = GenerateAccessToken(account.id);

                return new LoginResult
                {
                    Success = true,
                    Message = "登录成功",
                    Account = account,
                    AccessToken = accessToken
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"手机号登录时发生错误: {ex.Message}");
                return new LoginResult { Success = false, Message = "登录失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 令牌登录
        /// </summary>
        /// <param name="accessToken">访问令牌</param>
        /// <returns>登录结果</returns>
        public static LoginResult LoginByToken(string accessToken)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(accessToken))
                    return new LoginResult { Success = false, Message = "访问令牌不能为空" };

                // 验证令牌
                var accountId = ValidateAccessToken(accessToken);
                if (string.IsNullOrWhiteSpace(accountId))
                    return new LoginResult { Success = false, Message = "访问令牌无效或已过期" };

                // 查找用户
                var account = Account.getByid(accountId);
                if (account == null)
                    return new LoginResult { Success = false, Message = "用户不存在" };

                // 检查账号是否被删除
                if (account.deleted == true)
                    return new LoginResult { Success = false, Message = "账号已被禁用" };

                return new LoginResult
                {
                    Success = true,
                    Message = "令牌验证成功",
                    Account = account,
                    AccessToken = accessToken
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"令牌登录时发生错误: {ex.Message}");
                return new LoginResult { Success = false, Message = "令牌验证失败" };
            }
        }

        #endregion

        #region 验证码功能

        /// <summary>
        /// 发送邮箱验证码
        /// </summary>
        /// <param name="email">邮箱地址</param>
        /// <returns>发送结果</returns>
        public static SendCodeResult SendEmailVerificationCode(string email)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(email))
                    return new SendCodeResult { Success = false, Message = "邮箱不能为空" };

                if (!IsValidEmail(email))
                    return new SendCodeResult { Success = false, Message = "邮箱格式不正确" };

                // 检查邮箱是否已注册
                var account = Account.getByemail(email);
                if (account == null)
                    return new SendCodeResult { Success = false, Message = "邮箱未注册" };

                // 生成6位数字验证码
                var verificationCode = GenerateVerificationCode();

                // 存储验证码（这里可以存储到缓存或数据库中，设置过期时间）
                StoreVerificationCode(email, verificationCode, VerificationCodeType.Email);

                // TODO: 实际发送邮件的逻辑
                // 这里留空，由调用方实现具体的邮件发送逻辑
                var emailSent = SendEmailCode(email, verificationCode);

                if (emailSent)
                {
                    return new SendCodeResult { Success = true, Message = "验证码已发送到您的邮箱" };
                }
                else
                {
                    return new SendCodeResult { Success = false, Message = "验证码发送失败，请稍后重试" };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送邮箱验证码时发生错误: {ex.Message}");
                return new SendCodeResult { Success = false, Message = "验证码发送失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 发送手机验证码
        /// </summary>
        /// <param name="mobile">手机号码</param>
        /// <returns>发送结果</returns>
        public static SendCodeResult SendMobileVerificationCode(string mobile)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(mobile))
                    return new SendCodeResult { Success = false, Message = "手机号不能为空" };

                if (!IsValidMobile(mobile))
                    return new SendCodeResult { Success = false, Message = "手机号格式不正确" };

                // 检查手机号是否已注册
                var account = Account.getBymobile(mobile);
                if (account == null)
                    return new SendCodeResult { Success = false, Message = "手机号未注册" };

                // 生成6位数字验证码
                var verificationCode = GenerateVerificationCode();

                // 存储验证码（这里可以存储到缓存或数据库中，设置过期时间）
                StoreVerificationCode(mobile, verificationCode, VerificationCodeType.Mobile);

                // TODO: 实际发送短信的逻辑
                // 这里留空，由调用方实现具体的短信发送逻辑
                var smsSent = SendMobileCode(mobile, verificationCode);

                if (smsSent)
                {
                    return new SendCodeResult { Success = true, Message = "验证码已发送到您的手机" };
                }
                else
                {
                    return new SendCodeResult { Success = false, Message = "验证码发送失败，请稍后重试" };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送手机验证码时发生错误: {ex.Message}");
                return new SendCodeResult { Success = false, Message = "验证码发送失败，请稍后重试" };
            }
        }

        #endregion

        #region 忘记密码功能

        /// <summary>
        /// 通过邮箱验证码重置密码
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <param name="verificationCode">验证码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>重置结果</returns>
        public static ResetPasswordResult ResetPasswordByEmail(string email, string verificationCode, string newPassword)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(email))
                    return new ResetPasswordResult { Success = false, Message = "邮箱不能为空" };

                if (string.IsNullOrWhiteSpace(verificationCode))
                    return new ResetPasswordResult { Success = false, Message = "验证码不能为空" };

                if (!IsValidEmail(email))
                    return new ResetPasswordResult { Success = false, Message = "邮箱格式不正确" };

                // 验证新密码
                var passwordValidation = ValidatePassword(newPassword);
                if (!passwordValidation.IsValid)
                    return new ResetPasswordResult { Success = false, Message = passwordValidation.Message };

                // 验证验证码
                if (!VerifyVerificationCode(email, verificationCode, VerificationCodeType.Email))
                    return new ResetPasswordResult { Success = false, Message = "验证码错误或已过期" };

                // 查找用户
                var account = Account.getByemail(email);
                if (account == null)
                    return new ResetPasswordResult { Success = false, Message = "邮箱未注册" };

                // 更新密码
                account.password = HashPassword(newPassword);

                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();

                // 清除已使用的验证码
                ClearVerificationCode(email, VerificationCodeType.Email);

                return new ResetPasswordResult { Success = true, Message = "密码重置成功" };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"邮箱重置密码时发生错误: {ex.Message}");
                return new ResetPasswordResult { Success = false, Message = "密码重置失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 通过手机验证码重置密码
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <param name="verificationCode">验证码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>重置结果</returns>
        public static ResetPasswordResult ResetPasswordByMobile(string mobile, string verificationCode, string newPassword)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(mobile))
                    return new ResetPasswordResult { Success = false, Message = "手机号不能为空" };

                if (string.IsNullOrWhiteSpace(verificationCode))
                    return new ResetPasswordResult { Success = false, Message = "验证码不能为空" };

                if (!IsValidMobile(mobile))
                    return new ResetPasswordResult { Success = false, Message = "手机号格式不正确" };

                // 验证新密码
                var passwordValidation = ValidatePassword(newPassword);
                if (!passwordValidation.IsValid)
                    return new ResetPasswordResult { Success = false, Message = passwordValidation.Message };

                // 验证验证码
                if (!VerifyVerificationCode(mobile, verificationCode, VerificationCodeType.Mobile))
                    return new ResetPasswordResult { Success = false, Message = "验证码错误或已过期" };

                // 查找用户
                var account = Account.getBymobile(mobile);
                if (account == null)
                    return new ResetPasswordResult { Success = false, Message = "手机号未注册" };

                // 更新密码
                account.password = HashPassword(newPassword);

                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();

                // 清除已使用的验证码
                ClearVerificationCode(mobile, VerificationCodeType.Mobile);

                return new ResetPasswordResult { Success = true, Message = "密码重置成功" };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"手机号重置密码时发生错误: {ex.Message}");
                return new ResetPasswordResult { Success = false, Message = "密码重置失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 获取用户的安全问题
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>安全问题获取结果</returns>
        public static GetSecurityQuestionsResult GetSecurityQuestions(string username)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username))
                    return new GetSecurityQuestionsResult { Success = false, Message = "用户名不能为空" };

                // 查找用户
                var account = Account.getByusrname(username);
                if (account == null)
                    return new GetSecurityQuestionsResult { Success = false, Message = "用户名不存在" };

                // 检查是否设置了安全问题
                if (account.security_question == null || account.security_question.Length == 0)
                    return new GetSecurityQuestionsResult { Success = false, Message = "该账号未设置安全问题" };

                return new GetSecurityQuestionsResult
                {
                    Success = true,
                    Message = "获取安全问题成功",
                    SecurityQuestions = account.security_question
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取安全问题时发生错误: {ex.Message}");
                return new GetSecurityQuestionsResult { Success = false, Message = "获取安全问题失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 通过安全问题重置密码
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="securityAnswers">安全问题答案</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>重置结果</returns>
        public static ResetPasswordResult ResetPasswordBySecurityQuestion(string username, string[] securityAnswers, string newPassword)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username))
                    return new ResetPasswordResult { Success = false, Message = "用户名不能为空" };

                if (securityAnswers == null || securityAnswers.Length == 0)
                    return new ResetPasswordResult { Success = false, Message = "安全问题答案不能为空" };

                // 验证新密码
                var passwordValidation = ValidatePassword(newPassword);
                if (!passwordValidation.IsValid)
                    return new ResetPasswordResult { Success = false, Message = passwordValidation.Message };

                // 查找用户
                var account = Account.getByusrname(username);
                if (account == null)
                    return new ResetPasswordResult { Success = false, Message = "用户名不存在" };

                // 验证安全问题答案
                if (account.security_answer == null || account.security_answer.Length == 0)
                    return new ResetPasswordResult { Success = false, Message = "该账号未设置安全问题" };

                if (!VerifySecurityAnswers(securityAnswers, account.security_answer))
                    return new ResetPasswordResult { Success = false, Message = "安全问题答案错误" };

                // 更新密码
                account.password = HashPassword(newPassword);

                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();

                return new ResetPasswordResult { Success = true, Message = "密码重置成功" };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"安全问题重置密码时发生错误: {ex.Message}");
                return new ResetPasswordResult { Success = false, Message = "密码重置失败，请稍后重试" };
            }
        }

        #endregion

        #region 账号管理功能

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <param name="oldPassword">旧密码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>修改结果</returns>
        public static ChangePasswordResult ChangePassword(string accountId, string oldPassword, string newPassword)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(accountId))
                    return new ChangePasswordResult { Success = false, Message = "账号ID不能为空" };

                if (string.IsNullOrWhiteSpace(oldPassword))
                    return new ChangePasswordResult { Success = false, Message = "旧密码不能为空" };

                if (string.IsNullOrWhiteSpace(newPassword))
                    return new ChangePasswordResult { Success = false, Message = "新密码不能为空" };

                // 验证新密码强度
                var passwordValidation = ValidatePassword(newPassword);
                if (!passwordValidation.IsValid)
                    return new ChangePasswordResult { Success = false, Message = passwordValidation.Message };

                // 查找用户
                var account = Account.getByid(accountId);
                if (account == null)
                    return new ChangePasswordResult { Success = false, Message = "账号不存在" };

                // 验证旧密码
                if (!VerifyPassword(oldPassword, account.password))
                    return new ChangePasswordResult { Success = false, Message = "旧密码错误" };

                // 更新密码
                account.password = HashPassword(newPassword);

                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();

                return new ChangePasswordResult { Success = true, Message = "密码修改成功" };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"修改密码时发生错误: {ex.Message}");
                return new ChangePasswordResult { Success = false, Message = "密码修改失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 设置安全问题
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <param name="securityQuestions">安全问题</param>
        /// <param name="securityAnswers">安全答案</param>
        /// <returns>设置结果</returns>
        public static SetSecurityResult SetSecurityQuestions(string accountId, string[] securityQuestions, string[] securityAnswers)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(accountId))
                    return new SetSecurityResult { Success = false, Message = "账号ID不能为空" };

                if (securityQuestions == null || securityQuestions.Length == 0)
                    return new SetSecurityResult { Success = false, Message = "安全问题不能为空" };

                if (securityAnswers == null || securityAnswers.Length == 0)
                    return new SetSecurityResult { Success = false, Message = "安全答案不能为空" };

                if (securityQuestions.Length != securityAnswers.Length)
                    return new SetSecurityResult { Success = false, Message = "安全问题和答案数量不匹配" };

                // 查找用户
                var account = Account.getByid(accountId);
                if (account == null)
                    return new SetSecurityResult { Success = false, Message = "账号不存在" };

                // 设置安全问题和答案
                account.security_question = securityQuestions;
                account.security_answer = HashSecurityAnswers(securityAnswers);

                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();

                return new SetSecurityResult { Success = true, Message = "安全问题设置成功" };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置安全问题时发生错误: {ex.Message}");
                return new SetSecurityResult { Success = false, Message = "安全问题设置失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 启用/禁用二次验证
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <param name="enable">是否启用</param>
        /// <returns>设置结果</returns>
        public static TwoFactorResult SetTwoFactorAuth(string accountId, bool enable)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(accountId))
                    return new TwoFactorResult { Success = false, Message = "账号ID不能为空" };

                // 查找用户
                var account = Account.getByid(accountId);
                if (account == null)
                    return new TwoFactorResult { Success = false, Message = "账号不存在" };

                // 设置二次验证
                account.two_factor_auth = enable;

                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();

                return new TwoFactorResult
                {
                    Success = true,
                    Message = enable ? "二次验证已启用" : "二次验证已禁用"
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置二次验证时发生错误: {ex.Message}");
                return new TwoFactorResult { Success = false, Message = "二次验证设置失败，请稍后重试" };
            }
        }

        /// <summary>
        /// 更新账号信息
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <param name="nickname">昵称</param>
        /// <param name="avatar">头像</param>
        /// <param name="realName">真实姓名</param>
        /// <returns>更新结果</returns>
        public static UpdateAccountResult UpdateAccountInfo(string accountId, string nickname = null, string avatar = null, string realName = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(accountId))
                    return new UpdateAccountResult { Success = false, Message = "账号ID不能为空" };

                // 查找用户
                var account = Account.getByid(accountId);
                if (account == null)
                    return new UpdateAccountResult { Success = false, Message = "账号不存在" };

                // 更新信息
                if (!string.IsNullOrWhiteSpace(nickname))
                    account.nickname = nickname;

                if (!string.IsNullOrWhiteSpace(avatar))
                    account.avatar = avatar;

                if (!string.IsNullOrWhiteSpace(realName))
                    account.real_name = realName;

                // 保存到数据库
                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();

                return new UpdateAccountResult { Success = true, Message = "账号信息更新成功" };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新账号信息时发生错误: {ex.Message}");
                return new UpdateAccountResult { Success = false, Message = "账号信息更新失败，请稍后重试" };
            }
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 生成唯一ID
        /// </summary>
        /// <returns>唯一ID</returns>
        private static string GenerateUniqueId()
        {
            return Guid.NewGuid().ToString("N");
        }

        /// <summary>
        /// 生成6位数字验证码
        /// </summary>
        /// <returns>验证码</returns>
        private static string GenerateVerificationCode()
        {
            var random = new Random();
            return random.Next(100000, 999999).ToString();
        }

        /// <summary>
        /// 存储验证码（简单内存存储，实际项目中应该使用Redis等缓存）
        /// </summary>
        /// <param name="key">键（邮箱或手机号）</param>
        /// <param name="code">验证码</param>
        /// <param name="type">验证码类型</param>
        private static void StoreVerificationCode(string key, string code, VerificationCodeType type)
        {
            var cacheKey = $"{type}_{key}";
            var expireTime = DateTime.UtcNow.AddMinutes(5); // 5分钟过期

            // 简单的内存存储，实际项目中应该使用Redis
            if (!_verificationCodes.ContainsKey(cacheKey))
            {
                _verificationCodes[cacheKey] = new VerificationCodeInfo();
            }

            _verificationCodes[cacheKey].Code = code;
            _verificationCodes[cacheKey].ExpireTime = expireTime;
        }

        /// <summary>
        /// 验证验证码
        /// </summary>
        /// <param name="key">键（邮箱或手机号）</param>
        /// <param name="code">验证码</param>
        /// <param name="type">验证码类型</param>
        /// <returns>是否验证成功</returns>
        private static bool VerifyVerificationCode(string key, string code, VerificationCodeType type)
        {
            var cacheKey = $"{type}_{key}";

            if (!_verificationCodes.ContainsKey(cacheKey))
                return false;

            var codeInfo = _verificationCodes[cacheKey];

            // 检查是否过期
            if (DateTime.UtcNow > codeInfo.ExpireTime)
            {
                _verificationCodes.Remove(cacheKey);
                return false;
            }

            // 验证码是否匹配
            return codeInfo.Code == code;
        }

        /// <summary>
        /// 清除验证码
        /// </summary>
        /// <param name="key">键（邮箱或手机号）</param>
        /// <param name="type">验证码类型</param>
        private static void ClearVerificationCode(string key, VerificationCodeType type)
        {
            var cacheKey = $"{type}_{key}";
            if (_verificationCodes.ContainsKey(cacheKey))
            {
                _verificationCodes.Remove(cacheKey);
            }
        }

        /// <summary>
        /// 发送邮箱验证码（留空，由调用方实现）
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <param name="code">验证码</param>
        /// <returns>是否发送成功</returns>
        private static bool SendEmailCode(string email, string code)
        {
            // TODO: 实现邮件发送逻辑
            // 这里可以集成邮件服务提供商的API，如SendGrid、阿里云邮件推送等
            Console.WriteLine($"发送邮箱验证码到 {email}: {code}");
            return true; // 暂时返回true，实际应该根据发送结果返回
        }

        /// <summary>
        /// 发送手机验证码（留空，由调用方实现）
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <param name="code">验证码</param>
        /// <returns>是否发送成功</returns>
        private static bool SendMobileCode(string mobile, string code)
        {
            // TODO: 实现短信发送逻辑
            // 这里可以集成短信服务提供商的API，如阿里云短信、腾讯云短信等
            Console.WriteLine($"发送手机验证码到 {mobile}: {code}");
            return true; // 暂时返回true，实际应该根据发送结果返回
        }

        /// <summary>
        /// 密码哈希
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <returns>哈希后的密码</returns>
        private static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SALT_KEY"));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <param name="hashedPassword">哈希密码</param>
        /// <returns>是否匹配</returns>
        private static bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hashedPassword))
                return false;

            var hashToVerify = HashPassword(password);
            return hashToVerify == hashedPassword;
        }

        /// <summary>
        /// 验证密码强度
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>验证结果</returns>
        private static PasswordValidationResult ValidatePassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return new PasswordValidationResult { IsValid = false, Message = "密码不能为空" };

            if (password.Length < MIN_PASSWORD_LENGTH)
                return new PasswordValidationResult { IsValid = false, Message = $"密码长度不能少于{MIN_PASSWORD_LENGTH}位" };

            if (password.Length > MAX_PASSWORD_LENGTH)
                return new PasswordValidationResult { IsValid = false, Message = $"密码长度不能超过{MAX_PASSWORD_LENGTH}位" };

            // 检查是否包含字母和数字
            bool hasLetter = password.Any(char.IsLetter);
            bool hasDigit = password.Any(char.IsDigit);

            if (!hasLetter || !hasDigit)
                return new PasswordValidationResult { IsValid = false, Message = "密码必须包含字母和数字" };

            return new PasswordValidationResult { IsValid = true, Message = "密码强度符合要求" };
        }

        /// <summary>
        /// 验证邮箱格式
        /// </summary>
        /// <param name="email">邮箱</param>
        /// <returns>是否有效</returns>
        private static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var emailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证手机号格式
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <returns>是否有效</returns>
        private static bool IsValidMobile(string mobile)
        {
            if (string.IsNullOrWhiteSpace(mobile))
                return false;

            try
            {
                var mobileRegex = new Regex(@"^1[3-9]\d{9}$");
                return mobileRegex.IsMatch(mobile);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 生成访问令牌
        /// </summary>
        /// <param name="accountId">账号ID</param>
        /// <returns>访问令牌</returns>
        private static string GenerateAccessToken(string accountId)
        {
            var tokenData = $"{accountId}:{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}:{Guid.NewGuid():N}";
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(tokenData));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// 验证访问令牌
        /// </summary>
        /// <param name="accessToken">访问令牌</param>
        /// <returns>账号ID，如果无效则返回null</returns>
        private static string ValidateAccessToken(string accessToken)
        {
            try
            {
                // 查找具有该令牌的账号
                var account = Account.getByaccessToken(accessToken);
                if (account == null)
                    return null;

                // 检查令牌是否过期（这里简化处理，实际应该有更复杂的令牌验证逻辑）
                return account.id;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 更新登录信息
        /// </summary>
        /// <param name="account">账号</param>
        /// <param name="clientIp">客户端IP</param>
        private static void UpdateLoginInfo(Account account, string clientIp)
        {
            try
            {
                account.lastLogin = account.currentLogin;
                account.currentLogin = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                account.lastIP = account.ip;
                account.ip = clientIp;

                var db = ORMTables.Instance;
                db.Accounts.Update(account);
                db.SaveChanges();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新登录信息时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 哈希安全问题答案
        /// </summary>
        /// <param name="answers">答案数组</param>
        /// <returns>哈希后的答案数组</returns>
        private static string[] HashSecurityAnswers(string[] answers)
        {
            if (answers == null || answers.Length == 0)
                return null;

            var hashedAnswers = new string[answers.Length];
            for (int i = 0; i < answers.Length; i++)
            {
                hashedAnswers[i] = HashPassword(answers[i].ToLower().Trim());
            }
            return hashedAnswers;
        }

        /// <summary>
        /// 验证安全问题答案
        /// </summary>
        /// <param name="inputAnswers">输入的答案</param>
        /// <param name="storedAnswers">存储的答案</param>
        /// <returns>是否匹配</returns>
        private static bool VerifySecurityAnswers(string[] inputAnswers, string[] storedAnswers)
        {
            if (inputAnswers == null || storedAnswers == null)
                return false;

            if (inputAnswers.Length != storedAnswers.Length)
                return false;

            for (int i = 0; i < inputAnswers.Length; i++)
            {
                var hashedInput = HashPassword(inputAnswers[i].ToLower().Trim());
                if (hashedInput != storedAnswers[i])
                    return false;
            }

            return true;
        }

        #endregion
    }

    #region 枚举定义

    /// <summary>
    /// 验证码类型
    /// </summary>
    public enum VerificationCodeType
    {
        /// <summary>
        /// 邮箱验证码
        /// </summary>
        Email,

        /// <summary>
        /// 手机验证码
        /// </summary>
        Mobile
    }

    #endregion

    #region 辅助类定义

    /// <summary>
    /// 验证码信息
    /// </summary>
    public class VerificationCodeInfo
    {
        /// <summary>
        /// 验证码
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpireTime { get; set; }
    }

    #endregion

    #region 结果类定义

    /// <summary>
    /// 注册结果
    /// </summary>
    public class RegisterResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string AccountId { get; set; } = string.Empty;
        public string AccessToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// 登录结果
    /// </summary>
    public class LoginResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Account? Account { get; set; }
        public string AccessToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// 发送验证码结果
    /// </summary>
    public class SendCodeResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 重置密码结果
    /// </summary>
    public class ResetPasswordResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取安全问题结果
    /// </summary>
    public class GetSecurityQuestionsResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string[]? SecurityQuestions { get; set; }
    }

    /// <summary>
    /// 修改密码结果
    /// </summary>
    public class ChangePasswordResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 设置安全问题结果
    /// </summary>
    public class SetSecurityResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 二次验证设置结果
    /// </summary>
    public class TwoFactorResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新账号信息结果
    /// </summary>
    public class UpdateAccountResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 密码验证结果
    /// </summary>
    public class PasswordValidationResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    #endregion
}
