﻿
using System;
using System.Collections;
using ToolsService;

namespace GameServer
{
    class AppConfig : AppConfigBase
    {
        public static AppConfig Instance { get; } = new AppConfig();


        public string VersionInfoExportPath { get; set; } = "./UnityAutoCode/VersionInfo/"; //生成 C# 类 输出路径

        public AppConfig()
        {


            environmentVariables = new Dictionary<string, string>();
            ///每次启动都去获取一次环境变量存起来
            AllVariables(EnvironmentVariableTarget.Machine);
            AllVariables(EnvironmentVariableTarget.User);


            string MYSQL_HOST = Environment.GetEnvironmentVariable("MYSQL_HOST");
            string MYSQL_DB = Environment.GetEnvironmentVariable("MYSQL_DB");
            string MYSQL_USER = Environment.GetEnvironmentVariable("MYSQL_USER");
            string MYSQL_PASSWORD = Environment.GetEnvironmentVariable("MYSQL_PASSWORD");
            string MYSQL_PORTString = Environment.GetEnvironmentVariable("MYSQL_PORT");
            

            ///育碧公司的潜规则，k8s的环境下默认通过环境变量来读取数据库的信息
            if (!string.IsNullOrEmpty(MYSQL_HOST))
            {
                mysqlServer = MYSQL_HOST;
                mysqlDatabaseName = MYSQL_DB;
                mysqlUser = MYSQL_USER;
                mysqlPasswrd = MYSQL_PASSWORD;

                version = 1.0f;
                save();
            }
            if (string.IsNullOrEmpty(MYSQL_USER)) { MYSQL_USER = mysqlUser; }
            if (string.IsNullOrEmpty(MYSQL_DB)) { MYSQL_DB = mysqlDatabaseName; }
            if (string.IsNullOrEmpty(MYSQL_HOST)) { MYSQL_HOST = mysqlServer; }

            if (string.IsNullOrEmpty(MYSQL_PASSWORD)) { MYSQL_PASSWORD = mysqlPasswrd; }

            int MYSQL_PORT = 3306;
            if (!string.IsNullOrEmpty(MYSQL_PORTString))
            {
                int.TryParse(MYSQL_PORTString, out MYSQL_PORT);
            }
            MYSQL_PORT = 60006;


            //mysqlConnectString = $"Server=0.0.0.0;Port={mysqlPort};Database={mysqlDatabaseName}; User=cafe;Password={mysqlPasswrd};";
            mysqlConnectString = $"Server={MYSQL_HOST};Port={MYSQL_PORT};Database={MYSQL_DB}; User={MYSQL_USER};Password={MYSQL_PASSWORD};";
            //Console.WriteLine($"mysql :{mysqlConnectString}");


        }

        public void AllVariables(EnvironmentVariableTarget tgt)
        {


            foreach (DictionaryEntry de in Environment.GetEnvironmentVariables(tgt))
            {

                environmentVariables[de.Key.ToString()] = de.Value.ToString();
                //Console.WriteLine("系统检测到环境变量："+"key:" + de.Key.ToString() + "  value:" + de.Value.ToString());
            }

        }
        /// <summary>
        /// 直接取出所有的环境变量
        /// </summary>
        public Dictionary<string, string> environmentVariables;
        public string mysqlConnectString { get; set; } = "";

        public float version { get; set; } = 0.1f;
        public string mysqlServer { get; set; } = "************";//"127.0.0.1";//localhost
        public int mysqlPort { get; set; } = 3306;
        public string mysqlUser { get; set; } = "root";
        public string mysqlPasswrd { get; set; } = "your_new_root_password";//"111111";
        public string mysqlDatabaseName { get; set; } = "worldgpt_zq";
        public bool ormLogState { get; set; } = false;
        public bool WriteDebugLog { get; set; } = false;






        public string redisServer { get; set; } = "************";//"localhost";
        public int redisPort { get; set; } = 6379;
        public string redisPwd { get; set; } = "111111";
        public bool redisLog { get; set; } = false;



        public int GameServerPort = 8004;

        public string masterServer { get; set; } = "";
        public string masterPort { get; set; } = "";




        public string excelResPath { get; set; } = "/baseExcel";
        public string excelbinDataPatch { get; set; } = "./exceldata";//生成 excel2进制数据 输出路径





        public string aliEndpoint = "oss-cn-shanghai.aliyuncs.com";
        public string aliAccessKeyId = "LTAIxycI8btCnVtS";
        public string aliAccessKeySecret = "xcHTsv6vRE8DW1uZJm88hGbEf8H6Ba";



        public string cosSecretId = "AKIDeHtR0dZrIh7UpEMsAJYoxypZDgEn2WzQ";
        public string cosSecretKey = "34ADB2lN0cAYsCQH5lMqYFpGj6MWjOzk";

        /// <summary>
        /// 是否启动时还原数据库
        /// </summary>
        public bool isUpLoadMysql{ get; set; } = false;
        /// <summary>
        /// 是否启动时还原数据库
        /// </summary>
        public bool isUpLoadBuffer { get; set; } = false;
        public bool isGmOpen { get; set; } = true;
        public bool isTestModle { get; set; } = false;


        /// <summary>
        /// 共享内存
        /// </summary>
        public string MemoryMapPath { get; set; } = "./MemoryMap.data";
        public string ExcelDllPath { get; internal set; }
        public string tsOutPatch { get; internal set; }
        public string defNamespace { get; internal set; } = "excelData";
        public string? ResRootPatch { get; internal set; }
        public string? ResRootExportPatch { get; internal set; }
        public string ormOutPatch { get; set; } = Environment.CurrentDirectory.Replace("\\", "/") + "/";
    }
}
