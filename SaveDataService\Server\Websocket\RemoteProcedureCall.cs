using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SaveDataService.Server.Websocket
{
    class RemoteProcedureCall
    {
        /// <summary>
        /// 远程调用的方法的type
        /// </summary>
        public Type currentType { get; set; }

        /// <summary>
        /// class本身
        /// </summary>
        public Type type { get; set; }
        /// <summary>
        /// 前端发出调用程序的起始时间
        /// </summary>
        public DateTime callTime { get; set; }
        /// <summary>
        /// 前端的回调方法id
        /// </summary>
        public ulong callid { get; set; }
        /// <summary>
        /// 远程调用超时的时间，如果超时了调用段决策抛弃该信息或者直接报错
        /// </summary>
        public uint timeout { get; set; }


        /// <summary>
        /// 远程调用的class名字
        /// </summary>
        public string className { get; set; }
        /// <summary>
        /// 远程调用的方法名字
        /// </summary>
        public string functionName { get; set; }
        /// <summary>
        /// 远程调用的参数类型
        /// </summary>
        public List<Type> argsType { get; set; }


        /// <summary>
        /// 远程调用的参数
        /// </summary>
        public List<dynamic> args { get; set; }

        /// <summary>
        /// 远程调用方法的返回类型
        /// </summary>
        public Type returnType { get; set; }

        /// <summary>
        /// 远程调用方法返回的值
        /// </summary>
        public string returnValue { get; set; }


        public string toTypescript()
        {

            bool hasSetGet = false;
            //判断是属性还是方法
            if (functionName.StartsWith("Get") || functionName.StartsWith("Set") || functionName.EndsWith("Get") || functionName.EndsWith("Set"))
            {
                hasSetGet = true;
            }



            string ts = "";

            if (hasSetGet)
            {

                ts += Environment.NewLine;
                ts += "public " + functionName.Split("_")[1] + ":" + cshareToTSType(returnType) + ";";
                ts += Environment.NewLine;

            }


            
            ts += Environment.NewLine;

            ts += "public static "+functionName+"(";
            if (args != null)
            {

                for (int i = 0; i < args.Count; i++)
                {
                    ts += argsType[i] == null ? "" : args[i]+": "+  cshareToTSType(argsType[i]);
                    if (i + 1 != args.Count) { 
                        ts += ", ";
                    }
                }
            }


            if (functionName.IndexOf("Get") != -1 || functionName.IndexOf("Set") != -1)
            {


            }
            else
            {
                ts += args.Count == 0 ? "" : ",";
                ts += "callBack:Function = null";
            }


            ts += ")";

            if (returnType == null)
            {
                ts += ":void";
            }
            else
            {
                ts += ":"+cshareToTSType(returnType);
            }


            ts += "{";


            ts += Environment.NewLine + "GameServerUtil.callFunction(";
            ts += "\""+className + "\",\"" + functionName + "\");"+ Environment.NewLine;


            if (returnType != null) { 
            switch (cshareToTSType(returnType))
            {
                case "string":
                    ts += "return \"\"";
                    break;

                case "number":
                    ts += "return -1";
                    break;
                default:
                    break;
            }
            }
            ts += Environment.NewLine+"}";
            ts += Environment.NewLine;
            return ts;
        }


        public string cshareToTSType(Type typevalue)
        {
            string tsType = "string";
            switch (typevalue.Name)
            {
                case "":

                break;
                default:
                    break;
            }



            return tsType;
        }
    }
}
